<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="32" height="32" fill="url(#bg)" />
  
  <!-- 连接线 -->
  <g stroke="white" stroke-width="1" stroke-linecap="round" fill="none">
    <line x1="8" y1="8" x2="16" y2="16" />
    <line x1="24" y1="8" x2="16" y2="16" />
    <line x1="16" y1="16" x2="8" y2="24" />
    <line x1="16" y1="16" x2="24" y2="24" />
  </g>
  
  <!-- 节点 -->
  <g fill="white">
    <circle cx="8" cy="8" r="2" />
    <circle cx="24" cy="8" r="2" />
    <circle cx="16" cy="16" r="2" />
    <circle cx="8" cy="24" r="2" />
    <circle cx="24" cy="24" r="2" />
  </g>
</svg>