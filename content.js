// Content Script - 注入到网页中监听网络活动

class ContentNetworkMonitor {
    constructor() {
        this.isMonitoring = false;
        this.originalFetch = window.fetch;
        this.originalXHROpen = XMLHttpRequest.prototype.open;
        this.originalXHRSend = XMLHttpRequest.prototype.send;
        
        this.initializeMonitoring();
        this.setupMessageListener();
    }

    initializeMonitoring() {
        // 拦截 fetch 请求
        window.fetch = (...args) => {
            if (this.isMonitoring) {
                this.interceptFetch(...args);
            }
            return this.originalFetch.apply(window, args);
        };

        // 拦截 XMLHttpRequest
        XMLHttpRequest.prototype.open = function(...args) {
            if (this.monitor && this.monitor.isMonitoring) {
                this.monitor.interceptXHROpen.call(this.monitor, this, ...args);
            }
            return this.originalXHROpen.apply(this, args);
        };

        XMLHttpRequest.prototype.send = function(...args) {
            if (this.monitor && this.monitor.isMonitoring) {
                this.monitor.interceptXHRSend.call(this.monitor, this, ...args);
            }
            return this.originalXHRSend.apply(this, args);
        };

        // 为XHR添加监控器引用
        XMLHttpRequest.prototype.monitor = this;
    }

    setupMessageListener() {
        // 监听来自background script的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'startContentMonitoring') {
                this.startMonitoring();
                sendResponse({ success: true });
            } else if (message.type === 'stopContentMonitoring') {
                this.stopMonitoring();
                sendResponse({ success: true });
            }
        });
    }

    startMonitoring() {
        this.isMonitoring = true;
        console.log('Content script 开始监控网络请求');
        
        // 监听页面资源加载
        this.observeResourceLoading();
    }

    stopMonitoring() {
        this.isMonitoring = false;
        console.log('Content script 停止监控网络请求');
    }

    async interceptFetch(...args) {
        const startTime = performance.now();
        let url, options = {};

        // 解析fetch参数
        if (typeof args[0] === 'string') {
            url = args[0];
            options = args[1] || {};
        } else if (args[0] instanceof Request) {
            url = args[0].url;
            options = {
                method: args[0].method,
                headers: args[0].headers,
                body: args[0].body
            };
        }

        try {
            const response = await this.originalFetch.apply(window, args);
            const endTime = performance.now();
            
            // 发送请求信息到background script
            this.sendRequestInfo({
                url: url,
                method: options.method || 'GET',
                statusCode: response.status,
                statusText: response.statusText,
                responseTime: Math.round(endTime - startTime),
                size: this.estimateResponseSize(response),
                timestamp: Date.now(),
                type: 'fetch'
            });

            return response;
        } catch (error) {
            const endTime = performance.now();
            
            this.sendRequestInfo({
                url: url,
                method: options.method || 'GET',
                statusCode: 0,
                statusText: 'Network Error',
                responseTime: Math.round(endTime - startTime),
                size: 0,
                timestamp: Date.now(),
                type: 'fetch',
                error: error.message
            });

            throw error;
        }
    }

    interceptXHROpen(xhr, method, url, async, user, password) {
        xhr._networkMonitor = {
            method: method,
            url: url,
            startTime: performance.now()
        };
    }

    interceptXHRSend(xhr, body) {
        if (!xhr._networkMonitor) return;

        const monitor = xhr._networkMonitor;
        monitor.startTime = performance.now();

        // 监听响应
        const originalOnReadyStateChange = xhr.onreadystatechange;
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4) {
                const endTime = performance.now();
                
                this.sendRequestInfo({
                    url: monitor.url,
                    method: monitor.method,
                    statusCode: xhr.status,
                    statusText: xhr.statusText,
                    responseTime: Math.round(endTime - monitor.startTime),
                    size: this.estimateXHRResponseSize(xhr),
                    timestamp: Date.now(),
                    type: 'xhr'
                });
            }

            if (originalOnReadyStateChange) {
                originalOnReadyStateChange.apply(xhr, arguments);
            }
        };
    }

    observeResourceLoading() {
        // 监听页面资源加载完成事件
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.analyzePageResources();
            });
        } else {
            this.analyzePageResources();
        }

        // 监听新添加的资源
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.analyzeNewResource(node);
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    analyzePageResources() {
        // 分析页面中的资源
        const resources = [
            ...document.querySelectorAll('script[src]'),
            ...document.querySelectorAll('link[href]'),
            ...document.querySelectorAll('img[src]'),
            ...document.querySelectorAll('iframe[src]')
        ];

        resources.forEach(element => {
            this.analyzeResourceElement(element);
        });
    }

    analyzeNewResource(element) {
        if (element.src || element.href) {
            this.analyzeResourceElement(element);
        }
    }

    analyzeResourceElement(element) {
        const url = element.src || element.href;
        if (!url || url.startsWith('data:') || url.startsWith('blob:')) return;

        const resourceType = this.getResourceType(element);
        
        this.sendRequestInfo({
            url: url,
            method: 'GET',
            statusCode: 200, // 假设成功加载
            statusText: 'OK',
            responseTime: 0, // 无法准确测量
            size: this.estimateResourceSize(resourceType),
            timestamp: Date.now(),
            type: resourceType
        });
    }

    getResourceType(element) {
        const tagName = element.tagName.toLowerCase();
        switch (tagName) {
            case 'script': return 'script';
            case 'link': 
                return element.rel === 'stylesheet' ? 'stylesheet' : 'other';
            case 'img': return 'image';
            case 'iframe': return 'sub_frame';
            default: return 'other';
        }
    }

    estimateResponseSize(response) {
        // 尝试从响应头获取Content-Length
        const contentLength = response.headers.get('content-length');
        if (contentLength) {
            return parseInt(contentLength);
        }

        // 根据响应类型估算
        const contentType = response.headers.get('content-type') || '';
        if (contentType.includes('json')) return 5000;
        if (contentType.includes('html')) return 20000;
        if (contentType.includes('css')) return 15000;
        if (contentType.includes('javascript')) return 25000;
        if (contentType.includes('image')) return 30000;
        
        return 10000; // 默认估算
    }

    estimateXHRResponseSize(xhr) {
        const contentLength = xhr.getResponseHeader('content-length');
        if (contentLength) {
            return parseInt(contentLength);
        }

        // 根据响应内容估算
        const responseText = xhr.responseText || '';
        return responseText.length * 2; // 粗略估算字节数
    }

    estimateResourceSize(type) {
        const sizeEstimates = {
            'script': 25000,
            'stylesheet': 15000,
            'image': 30000,
            'sub_frame': 20000,
            'other': 10000
        };
        return sizeEstimates[type] || 10000;
    }

    async sendRequestInfo(requestInfo) {
        try {
            await chrome.runtime.sendMessage({
                type: 'contentNetworkRequest',
                data: requestInfo
            });
        } catch (error) {
            console.log('发送请求信息失败:', error);
        }
    }
}

// 初始化内容脚本监控器
if (typeof window !== 'undefined') {
    const contentMonitor = new ContentNetworkMonitor();
    
    // 页面加载完成后自动开始监控
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            contentMonitor.startMonitoring();
        });
    } else {
        contentMonitor.startMonitoring();
    }
}
