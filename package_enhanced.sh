#!/bin/bash

# 网络流量分析器 Chrome 扩展打包脚本 - 增强版
# 添加重要数据包分析 + 时间线鼠标悬停功能

echo "开始打包网络流量分析器 Chrome 扩展 - 增强版..."

# 创建临时目录
TEMP_DIR="temp_package_enhanced"
ZIP_NAME="网络流量分析器-Chrome插件-增强版.zip"

# 清理之前的临时目录
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

# 创建新的临时目录
mkdir "$TEMP_DIR"

# 复制所有必要文件到临时目录
echo "复制文件..."
cp chrome-extension-package/manifest.json "$TEMP_DIR/"
cp chrome-extension-package/background.js "$TEMP_DIR/"
cp chrome-extension-package/content.js "$TEMP_DIR/"
cp chrome-extension-package/popup.html "$TEMP_DIR/"
cp chrome-extension-package/popup.css "$TEMP_DIR/"
cp chrome-extension-package/popup.js "$TEMP_DIR/"
cp chrome-extension-package/panel.html "$TEMP_DIR/"
cp chrome-extension-package/panel.css "$TEMP_DIR/"
cp chrome-extension-package/panel.js "$TEMP_DIR/"
cp chrome-extension-package/devtools.html "$TEMP_DIR/"
cp chrome-extension-package/devtools.js "$TEMP_DIR/"

# 复制图标目录
cp -r chrome-extension-package/icons "$TEMP_DIR/"

# 创建更新说明文件
cat > "$TEMP_DIR/ENHANCED_UPDATE_NOTES.txt" << 'EOF'
网络流量分析器 Chrome 扩展 - 增强版

🚀 新增功能：

1. 重要数据包分析 📊
   ✅ 在独立面板中添加了"重要数据包"标签页
   ✅ 支持多种过滤器：全部、身份认证、API请求、Cookies、敏感数据
   ✅ 智能识别不同类型的网络请求
   ✅ 彩色边框区分不同类型的数据包
   ✅ 显示详细的请求信息和域名

2. 时间线图表增强 📈
   ✅ 不同域名使用不同颜色的数据点
   ✅ 鼠标悬停显示详细信息tooltip
   ✅ 右侧显示域名颜色图例
   ✅ 改进的坐标轴和标签显示
   ✅ 基于相对时间的X轴显示

🎯 功能详情：

📊 重要数据包分析：
- 全部：显示所有网络请求
- 身份认证：login、auth、signin、signup、oauth、token相关请求
- API请求：包含/api/、/v1/、/v2/或xmlhttprequest类型的请求
- Cookies：包含cookie、session或相关的请求
- 敏感数据：包含敏感信息的请求

数据包类型颜色标识：
- 🔴 身份认证：红色边框
- 🔵 API请求：蓝色边框
- 🟡 Cookies：黄色边框
- 🟣 敏感数据：紫色边框

📈 时间线图表增强：
- 每个域名使用不同的颜色
- 15种鲜明的颜色循环使用
- 鼠标悬停显示：
  * 域名信息
  * HTTP方法
  * 响应状态码
  * 响应时间
  * 相对时间
  * 数据大小
  * 完整URL

图例功能：
- 右上角显示域名颜色对应关系
- 最多显示8个域名，超出显示"+X more..."
- 域名文本自动截断以适应显示

🔧 技术改进：

1. 数据包分析算法：
   - 智能URL关键词匹配
   - HTTP方法类型判断
   - 请求类型分类
   - 敏感数据检测

2. 时间线交互：
   - 精确的鼠标位置检测
   - 动态tooltip定位
   - 防止tooltip超出屏幕边界
   - 鼠标样式变化指示

3. 颜色管理：
   - 域名到颜色的一致性映射
   - 高对比度颜色选择
   - 图例自动布局

🎨 界面优化：

状态指示器：
- 🟢 实时监控中：绿色背景
- 🔴 连接失败：红色背景
- 最后更新时间显示

过滤按钮：
- 圆角设计，渐变背景
- 悬停效果和激活状态
- 响应式布局

数据包卡片：
- 悬停时轻微上浮效果
- 左侧彩色边框标识类型
- 清晰的信息层次结构

🔍 使用指南：

1. 基本使用：
   - 点击扩展图标 → 开始分析
   - 点击📌按钮 → 打开独立面板
   - 切换到"重要数据包"标签页

2. 数据包分析：
   - 使用顶部过滤按钮筛选不同类型
   - 查看彩色边框了解数据包类型
   - 点击查看详细信息

3. 时间线分析：
   - 切换到"时间线"标签页
   - 鼠标悬停在数据点上查看详情
   - 参考右侧图例了解域名对应关系
   - 观察请求的时间分布模式

💡 实用技巧：

- 使用"身份认证"过滤器快速找到登录相关请求
- 使用"API请求"过滤器分析应用的API调用
- 时间线图可以帮助识别页面加载的性能瓶颈
- 不同颜色的数据点可以快速识别哪些域名产生了最多请求

📋 版本信息：
- 版本：增强版
- 新增功能：重要数据包分析 + 时间线交互
- 兼容性：Chrome 88+
- 文件大小：约 65KB

现在独立面板功能更加完整和强大！
EOF

# 创建ZIP文件
echo "创建ZIP文件..."
cd "$TEMP_DIR"
zip -r "../$ZIP_NAME" .
cd ..

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "文件名: $ZIP_NAME"
echo ""
echo "🚀 新增功能："
echo "✅ 重要数据包分析（5种过滤器）"
echo "✅ 时间线鼠标悬停显示域名信息"
echo "✅ 不同域名使用不同颜色"
echo "✅ 域名颜色图例显示"
echo ""
echo "📊 重要数据包分析："
echo "✅ 智能识别身份认证、API、Cookies、敏感数据"
echo "✅ 彩色边框区分不同类型"
echo "✅ 详细的请求信息显示"
echo ""
echo "📈 时间线图表增强："
echo "✅ 15种颜色区分不同域名"
echo "✅ 鼠标悬停显示完整信息"
echo "✅ 右侧域名图例"
echo "✅ 基于相对时间的显示"
echo ""
echo "🎯 现在独立面板功能完整了！"
