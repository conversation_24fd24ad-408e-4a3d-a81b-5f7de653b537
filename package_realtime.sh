#!/bin/bash

# 网络流量分析器 Chrome 扩展打包脚本 - 实时同步版
# 修复独立面板数据实时同步问题

echo "开始打包网络流量分析器 Chrome 扩展 - 实时同步版..."

# 创建临时目录
TEMP_DIR="temp_package_realtime"
ZIP_NAME="网络流量分析器-Chrome插件-实时同步版.zip"

# 清理之前的临时目录
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

# 创建新的临时目录
mkdir "$TEMP_DIR"

# 复制所有必要文件到临时目录
echo "复制文件..."
cp chrome-extension-package/manifest.json "$TEMP_DIR/"
cp chrome-extension-package/background.js "$TEMP_DIR/"
cp chrome-extension-package/content.js "$TEMP_DIR/"
cp chrome-extension-package/popup.html "$TEMP_DIR/"
cp chrome-extension-package/popup.css "$TEMP_DIR/"
cp chrome-extension-package/popup.js "$TEMP_DIR/"
cp chrome-extension-package/panel.html "$TEMP_DIR/"
cp chrome-extension-package/panel.css "$TEMP_DIR/"
cp chrome-extension-package/panel.js "$TEMP_DIR/"
cp chrome-extension-package/devtools.html "$TEMP_DIR/"
cp chrome-extension-package/devtools.js "$TEMP_DIR/"

# 复制图标目录
cp -r chrome-extension-package/icons "$TEMP_DIR/"

# 创建更新说明文件
cat > "$TEMP_DIR/REALTIME_UPDATE_NOTES.txt" << 'EOF'
网络流量分析器 Chrome 扩展 - 实时同步版

🔧 主要修复：
✅ 独立面板数据实时同步问题
✅ 时间显示修复（显示真实递增时间）
✅ 📌按钮功能正确（打开独立面板）

🚀 新功能特性：

📌 独立面板实时同步：
- 点击📌按钮打开独立分析面板（900x700）
- 独立面板会自动恢复分析状态
- 实时接收网络请求数据
- 数据通过消息传递和存储双重同步
- 状态指示器显示连接状态

⏱️ 时间显示修复：
- 显示真实的递增相对时间
- 格式：+XXXms（从分析开始的毫秒数）
- 第一批：+0ms ~ +2000ms
- 第二批：+2150ms ~ +4320ms
- 依此类推...

🔗 数据同步机制：
1. 实时消息传递：background → 独立面板
2. 存储备份：数据同时保存到chrome.storage
3. 状态恢复：独立面板启动时恢复分析状态
4. 监控重启：自动重新启动网络监控

📊 界面增强：
- 连接状态指示器：🟢 实时监控中 / 🔴 连接失败
- 最后更新时间显示
- 实时数据刷新（每3秒）
- 增强的域名显示（400px宽度）

🎯 使用流程：
1. 点击扩展图标打开主界面
2. 点击"分析当前页面"开始监控
3. 点击📌按钮打开独立分析面板
4. 独立面板自动恢复分析状态并开始实时同步
5. 可以关闭主popup，独立面板继续工作
6. 一边操作网页，一边实时查看数据包分析

🔍 技术改进：
- 修复了独立面板的消息监听
- 添加了分析状态的存储和恢复
- 实现了监控的自动重启机制
- 优化了数据同步的双重保障
- 添加了详细的状态指示

📋 调试信息：
控制台会显示：
- "独立面板模式启动"
- "恢复分析状态: {tabId, isAnalyzing, startTime}"
- "重新启动网络监控"
- "独立面板收到新请求: URL +XXXms"
- "数据已保存到存储，独立面板可以访问"

🛠️ 问题解决：
✅ 独立面板打开后数据停止更新 → 已修复
✅ 时间显示全部为+0ms → 已修复
✅ 📌按钮跳转到错误页面 → 已修复
✅ 独立面板无法接收实时数据 → 已修复

💡 使用提示：
- 独立面板会显示"🟢 实时监控中"表示正常工作
- 如果显示"🔴 连接失败"，请重新开始分析
- 数据每3秒自动刷新，也可手动点击刷新按钮
- 独立面板可以调整大小和位置
- 关闭浏览器时所有面板会自动关闭

📊 版本信息：
- 版本：实时同步版
- 主要修复：独立面板数据同步
- 兼容性：Chrome 88+
- 文件大小：约 60KB

现在独立面板可以正常实时同步数据了！
EOF

# 创建ZIP文件
echo "创建ZIP文件..."
cd "$TEMP_DIR"
zip -r "../$ZIP_NAME" .
cd ..

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "文件名: $ZIP_NAME"
echo ""
echo "🔧 主要修复："
echo "✅ 独立面板数据实时同步"
echo "✅ 时间显示修复（真实递增时间）"
echo "✅ 📌按钮功能正确"
echo ""
echo "🚀 新功能："
echo "✅ 状态指示器（🟢 实时监控中）"
echo "✅ 最后更新时间显示"
echo "✅ 自动状态恢复"
echo "✅ 双重数据同步机制"
echo ""
echo "📌 现在独立面板可以："
echo "✅ 实时接收网络请求数据"
echo "✅ 显示真实的递增时间"
echo "✅ 一边操作网页一边查看分析"
echo "✅ 自动恢复分析状态"
echo ""
echo "🎯 问题已解决！可以正常使用了！"
