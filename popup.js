class NetworkAnalyzer {
    constructor() {
        this.isAnalyzing = false;
        this.currentTabId = null;
        this.domains = new Map();
        this.packets = [];
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0
        };
        
        this.initializeElements();
        this.bindEvents();
        this.loadStoredData();
    }

    initializeElements() {
        this.urlInput = document.getElementById('urlInput');
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.btnText = document.getElementById('btnText');
        this.btnLoader = document.getElementById('btnLoader');
        this.clearBtn = document.getElementById('clearBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.devtoolsBtn = document.getElementById('devtoolsBtn');
        this.statusText = document.getElementById('statusText');
        this.statusIndicator = document.getElementById('statusIndicator');
        
        // 统计元素
        this.totalRequestsEl = document.getElementById('totalRequests');
        this.uniqueDomainsEl = document.getElementById('uniqueDomains');
        this.totalSizeEl = document.getElementById('totalSize');
        
        // 列表元素
        this.domainsList = document.getElementById('domainsList');
        this.packetsList = document.getElementById('packetsList');
    }

    bindEvents() {
        // 分析按钮
        this.analyzeBtn.addEventListener('click', () => this.toggleAnalysis());
        
        // 快速URL按钮
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.urlInput.value = e.target.dataset.url;
            });
        });
        
        // 控制按钮
        this.clearBtn.addEventListener('click', () => this.clearData());
        this.exportBtn.addEventListener('click', () => this.exportReport());
        this.devtoolsBtn.addEventListener('click', () => this.openDevtools());
        
        // URL输入框回车
        this.urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.toggleAnalysis();
            }
        });
        
        // 监听来自background script的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'networkRequest') {
                this.handleNetworkRequest(message.data);
            }
        });
    }

    async toggleAnalysis() {
        if (this.isAnalyzing) {
            await this.stopAnalysis();
        } else {
            await this.startAnalysis();
        }
    }

    async startAnalysis() {
        const url = this.urlInput.value.trim();
        if (!url) {
            this.showError('请输入有效的URL');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showError('请输入有效的URL格式');
            return;
        }

        this.isAnalyzing = true;
        this.updateUI();
        this.setStatus('正在启动分析...', 'analyzing');

        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTabId = tab.id;

            // 清除之前的数据
            this.clearData();

            // 通知background script开始监听
            await chrome.runtime.sendMessage({
                type: 'startAnalysis',
                tabId: this.currentTabId,
                url: url
            });

            // 导航到目标URL
            await chrome.tabs.update(this.currentTabId, { url: url });

            this.setStatus('正在分析网络流量...', 'analyzing');
            
            // 开始模拟数据更新（用于演示）
            this.startSimulation();

        } catch (error) {
            console.error('启动分析失败:', error);
            this.showError('启动分析失败: ' + error.message);
            this.isAnalyzing = false;
            this.updateUI();
        }
    }

    async stopAnalysis() {
        this.isAnalyzing = false;
        this.updateUI();
        this.setStatus('分析已停止', 'ready');

        try {
            await chrome.runtime.sendMessage({
                type: 'stopAnalysis',
                tabId: this.currentTabId
            });
        } catch (error) {
            console.error('停止分析失败:', error);
        }

        this.stopSimulation();
    }

    startSimulation() {
        // 模拟网络请求数据（用于演示）
        this.simulationInterval = setInterval(() => {
            if (!this.isAnalyzing) return;

            const mockRequests = this.generateMockRequests();
            mockRequests.forEach(request => {
                this.handleNetworkRequest(request);
            });
        }, 2000);
    }

    stopSimulation() {
        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }
    }

    generateMockRequests() {
        const domains = [
            'cdn.jsdelivr.net',
            'fonts.googleapis.com',
            'www.google-analytics.com',
            'connect.facebook.net',
            'platform.twitter.com',
            'apis.google.com',
            'ajax.googleapis.com',
            'cdnjs.cloudflare.com'
        ];

        const methods = ['GET', 'POST', 'PUT', 'DELETE'];
        const statusCodes = [200, 201, 304, 404, 500];
        
        const requests = [];
        const count = Math.floor(Math.random() * 3) + 1;

        for (let i = 0; i < count; i++) {
            const domain = domains[Math.floor(Math.random() * domains.length)];
            const method = methods[Math.floor(Math.random() * methods.length)];
            const statusCode = statusCodes[Math.floor(Math.random() * statusCodes.length)];
            const size = Math.floor(Math.random() * 50000) + 1000;
            const responseTime = Math.floor(Math.random() * 500) + 50;

            requests.push({
                url: `https://${domain}/api/data?t=${Date.now()}`,
                method: method,
                statusCode: statusCode,
                size: size,
                responseTime: responseTime,
                timestamp: Date.now()
            });
        }

        return requests;
    }

    handleNetworkRequest(request) {
        try {
            const domain = new URL(request.url).hostname;
            
            // 更新域名统计
            if (this.domains.has(domain)) {
                const domainData = this.domains.get(domain);
                domainData.count++;
                domainData.totalSize += request.size;
                domainData.totalTime += request.responseTime;
                domainData.avgTime = Math.round(domainData.totalTime / domainData.count);
            } else {
                this.domains.set(domain, {
                    count: 1,
                    totalSize: request.size,
                    totalTime: request.responseTime,
                    avgTime: request.responseTime
                });
            }

            // 添加到数据包列表
            this.packets.unshift(request);
            if (this.packets.length > 50) {
                this.packets = this.packets.slice(0, 50);
            }

            // 更新统计
            this.stats.totalRequests++;
            this.stats.uniqueDomains = this.domains.size;
            this.stats.totalSize += request.size;

            // 更新UI
            this.updateStats();
            this.updateDomainsList();
            this.updatePacketsList();

            // 保存数据
            this.saveData();

        } catch (error) {
            console.error('处理网络请求失败:', error);
        }
    }

    updateUI() {
        if (this.isAnalyzing) {
            this.btnText.textContent = '停止分析';
            this.btnLoader.classList.remove('hidden');
            this.analyzeBtn.classList.add('analyzing');
        } else {
            this.btnText.textContent = '开始分析';
            this.btnLoader.classList.add('hidden');
            this.analyzeBtn.classList.remove('analyzing');
        }
    }

    updateStats() {
        this.totalRequestsEl.textContent = this.stats.totalRequests;
        this.uniqueDomainsEl.textContent = this.stats.uniqueDomains;
        this.totalSizeEl.textContent = this.formatSize(this.stats.totalSize);
    }

    updateDomainsList() {
        if (this.domains.size === 0) {
            this.domainsList.innerHTML = '<div class="empty-state"><p>开始分析后，这里将显示访问的域名</p></div>';
            return;
        }

        const sortedDomains = Array.from(this.domains.entries())
            .sort((a, b) => b[1].count - a[1].count);

        this.domainsList.innerHTML = sortedDomains.map(([domain, data]) => `
            <div class="domain-item">
                <div class="domain-name" title="${domain}">${domain}</div>
                <div class="domain-count">${data.count}</div>
            </div>
        `).join('');
    }

    updatePacketsList() {
        if (this.packets.length === 0) {
            this.packetsList.innerHTML = '<div class="empty-state"><p>开始分析后，这里将显示网络请求详情</p></div>';
            return;
        }

        this.packetsList.innerHTML = this.packets.slice(0, 10).map(packet => `
            <div class="packet-item">
                <div class="packet-url">
                    <span class="packet-method method-${packet.method}">${packet.method}</span>
                    ${this.truncateUrl(packet.url)}
                </div>
                <div class="packet-details">
                    状态: ${packet.statusCode} | 大小: ${this.formatSize(packet.size)} | 时间: ${packet.responseTime}ms
                </div>
            </div>
        `).join('');
    }

    clearData() {
        this.domains.clear();
        this.packets = [];
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0
        };

        this.updateStats();
        this.updateDomainsList();
        this.updatePacketsList();
        this.saveData();
    }

    async exportReport() {
        const report = {
            timestamp: new Date().toISOString(),
            url: this.urlInput.value,
            stats: this.stats,
            domains: Object.fromEntries(this.domains),
            packets: this.packets
        };

        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        await chrome.downloads.download({
            url: url,
            filename: `network-analysis-${Date.now()}.json`
        });
    }

    async openDevtools() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.update(tab.id, { active: true });
            // 注意：Chrome扩展无法直接打开DevTools，需要用户手动打开
            this.showInfo('请按F12打开开发者工具查看详细信息');
        } catch (error) {
            console.error('打开DevTools失败:', error);
        }
    }

    setStatus(text, type = 'ready') {
        this.statusText.textContent = text;
        this.statusIndicator.className = `status-indicator ${type}`;
    }

    showError(message) {
        this.setStatus(message, 'error');
        setTimeout(() => {
            this.setStatus('就绪', 'ready');
        }, 3000);
    }

    showInfo(message) {
        this.setStatus(message, 'ready');
        setTimeout(() => {
            this.setStatus('就绪', 'ready');
        }, 3000);
    }

    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    formatSize(bytes) {
        if (bytes === 0) return '0B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
    }

    truncateUrl(url, maxLength = 40) {
        if (url.length <= maxLength) return url;
        return url.substring(0, maxLength) + '...';
    }

    async saveData() {
        try {
            await chrome.storage.local.set({
                domains: Object.fromEntries(this.domains),
                packets: this.packets,
                stats: this.stats
            });
        } catch (error) {
            console.error('保存数据失败:', error);
        }
    }

    async loadStoredData() {
        try {
            const data = await chrome.storage.local.get(['domains', 'packets', 'stats']);
            
            if (data.domains) {
                this.domains = new Map(Object.entries(data.domains));
            }
            if (data.packets) {
                this.packets = data.packets;
            }
            if (data.stats) {
                this.stats = data.stats;
            }

            this.updateStats();
            this.updateDomainsList();
            this.updatePacketsList();
        } catch (error) {
            console.error('加载数据失败:', error);
        }
    }
}

// 初始化分析器
document.addEventListener('DOMContentLoaded', () => {
    new NetworkAnalyzer();
});
