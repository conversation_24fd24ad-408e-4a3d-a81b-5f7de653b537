#!/usr/bin/env python3
"""
简单的图标生成脚本
使用PIL库创建Chrome插件所需的图标文件
"""

try:
    from PIL import Image, ImageDraw
    import os
except ImportError:
    print("请先安装PIL库: pip install Pillow")
    exit(1)

def create_icon(size):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制渐变背景（简化为纯色）
    background_color = (102, 126, 234)  # #667eea
    draw.rectangle([0, 0, size, size], fill=background_color)
    
    # 计算节点位置（按比例缩放）
    scale = size / 128
    nodes = [
        (int(32 * scale), int(32 * scale)),
        (int(96 * scale), int(32 * scale)),
        (int(64 * scale), int(64 * scale)),
        (int(32 * scale), int(96 * scale)),
        (int(96 * scale), int(96 * scale))
    ]
    
    # 绘制连接线
    line_width = max(1, int(3 * scale))
    line_color = (255, 255, 255)
    
    # 连接线
    draw.line([nodes[0], nodes[2]], fill=line_color, width=line_width)
    draw.line([nodes[1], nodes[2]], fill=line_color, width=line_width)
    draw.line([nodes[2], nodes[3]], fill=line_color, width=line_width)
    draw.line([nodes[2], nodes[4]], fill=line_color, width=line_width)
    
    # 绘制节点
    node_radius = max(2, int(6 * scale))
    for node in nodes:
        x, y = node
        draw.ellipse([x-node_radius, y-node_radius, x+node_radius, y+node_radius], 
                    fill=line_color)
    
    return img

def main():
    """主函数"""
    # 确保icons目录存在
    icons_dir = "icons"
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    # 创建所需尺寸的图标
    sizes = [16, 32, 48, 128]
    
    print("🎨 开始生成图标文件...")
    
    for size in sizes:
        icon = create_icon(size)
        filename = f"{icons_dir}/icon{size}.png"
        icon.save(filename, "PNG")
        print(f"✅ 已生成: {filename}")
    
    print("🎉 所有图标文件生成完成！")
    print("\n现在可以重新安装Chrome插件了：")
    print("1. 打开 chrome://extensions/")
    print("2. 开启'开发者模式'")
    print("3. 点击'加载已解压的扩展程序'")
    print("4. 选择 chrome-extension-package 文件夹")

if __name__ == "__main__":
    main()
