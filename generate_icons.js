// Node.js脚本生成图标文件
const fs = require('fs');
const path = require('path');

// 创建SVG图标内容
function createSVGIcon(size) {
    const scale = size / 128;
    const strokeWidth = Math.max(1, 3 * scale);
    const nodeRadius = Math.max(2, 6 * scale);
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="${size}" height="${size}" fill="url(#bg)" />
  
  <!-- 连接线 -->
  <g stroke="white" stroke-width="${strokeWidth}" stroke-linecap="round" fill="none">
    <line x1="${32 * scale}" y1="${32 * scale}" x2="${64 * scale}" y2="${64 * scale}" />
    <line x1="${96 * scale}" y1="${32 * scale}" x2="${64 * scale}" y2="${64 * scale}" />
    <line x1="${64 * scale}" y1="${64 * scale}" x2="${32 * scale}" y2="${96 * scale}" />
    <line x1="${64 * scale}" y1="${64 * scale}" x2="${96 * scale}" y2="${96 * scale}" />
  </g>
  
  <!-- 节点 -->
  <g fill="white">
    <circle cx="${32 * scale}" cy="${32 * scale}" r="${nodeRadius}" />
    <circle cx="${96 * scale}" cy="${32 * scale}" r="${nodeRadius}" />
    <circle cx="${64 * scale}" cy="${64 * scale}" r="${nodeRadius}" />
    <circle cx="${32 * scale}" cy="${96 * scale}" r="${nodeRadius}" />
    <circle cx="${96 * scale}" cy="${96 * scale}" r="${nodeRadius}" />
  </g>
</svg>`;
}

// 确保icons目录存在
const iconsDir = path.join(__dirname, 'icons');
if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir);
}

const iconsPackageDir = path.join(__dirname, 'chrome-extension-package', 'icons');
if (!fs.existsSync(iconsPackageDir)) {
    fs.mkdirSync(iconsPackageDir, { recursive: true });
}

// 生成不同尺寸的SVG图标
const sizes = [16, 32, 48, 128];

console.log('🎨 开始生成图标文件...');

sizes.forEach(size => {
    const svgContent = createSVGIcon(size);
    
    // 保存到两个位置
    const filename1 = path.join(iconsDir, `icon${size}.svg`);
    const filename2 = path.join(iconsPackageDir, `icon${size}.svg`);
    
    fs.writeFileSync(filename1, svgContent);
    fs.writeFileSync(filename2, svgContent);
    
    console.log(`✅ 已生成: icon${size}.svg`);
});

console.log('🎉 SVG图标文件生成完成！');
console.log('\n📝 注意：Chrome插件需要PNG格式的图标');
console.log('请使用以下方法之一转换为PNG：');
console.log('1. 在浏览器中打开 create_icons_simple.html');
console.log('2. 使用在线SVG转PNG工具');
console.log('3. 使用图像编辑软件转换');
