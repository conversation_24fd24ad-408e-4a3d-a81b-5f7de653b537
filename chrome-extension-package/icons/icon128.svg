<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="128" height="128" fill="url(#bg)" />
  
  <!-- 连接线 -->
  <g stroke="white" stroke-width="3" stroke-linecap="round" fill="none">
    <line x1="32" y1="32" x2="64" y2="64" />
    <line x1="96" y1="32" x2="64" y2="64" />
    <line x1="64" y1="64" x2="32" y2="96" />
    <line x1="64" y1="64" x2="96" y2="96" />
  </g>
  
  <!-- 节点 -->
  <g fill="white">
    <circle cx="32" cy="32" r="6" />
    <circle cx="96" cy="32" r="6" />
    <circle cx="64" cy="64" r="6" />
    <circle cx="32" cy="96" r="6" />
    <circle cx="96" cy="96" r="6" />
  </g>
</svg>