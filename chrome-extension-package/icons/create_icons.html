<!DOCTYPE html>
<html>
<head>
    <title>生成插件图标</title>
</head>
<body>
    <canvas id="canvas" width="128" height="128"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 128, 128);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        
        // 绘制背景
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 128, 128);
        
        // 绘制网络图标
        ctx.strokeStyle = 'white';
        ctx.fillStyle = 'white';
        ctx.lineWidth = 3;
        
        // 绘制节点
        const nodes = [
            {x: 32, y: 32}, {x: 96, y: 32},
            {x: 64, y: 64}, {x: 32, y: 96}, {x: 96, y: 96}
        ];
        
        // 绘制连接线
        ctx.beginPath();
        ctx.moveTo(32, 32);
        ctx.lineTo(64, 64);
        ctx.lineTo(96, 32);
        ctx.moveTo(64, 64);
        ctx.lineTo(32, 96);
        ctx.moveTo(64, 64);
        ctx.lineTo(96, 96);
        ctx.stroke();
        
        // 绘制节点圆圈
        nodes.forEach(node => {
            ctx.beginPath();
            ctx.arc(node.x, node.y, 6, 0, 2 * Math.PI);
            ctx.fill();
        });
        
        // 下载不同尺寸的图标
        function downloadIcon(size) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = size;
            tempCanvas.height = size;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(canvas, 0, 0, size, size);
            
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = tempCanvas.toDataURL();
            link.click();
        }
        
        // 自动下载所有尺寸
        setTimeout(() => {
            [16, 32, 48, 128].forEach(size => {
                setTimeout(() => downloadIcon(size), size * 10);
            });
        }, 1000);
    </script>
    <p>图标将自动下载，请将下载的图标文件放入 icons 文件夹中</p>
</body>
</html>
