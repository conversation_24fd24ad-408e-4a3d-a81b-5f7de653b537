<!DOCTYPE html>
<html>
<head>
    <title>生成插件图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        canvas { border: 1px solid #ccc; margin: 20px; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>🎨 生成插件图标</h1>
    <p>点击下面的按钮生成并下载图标文件</p>
    
    <canvas id="canvas" width="128" height="128"></canvas>
    
    <div>
        <button onclick="downloadIcon(16)">下载 16x16</button>
        <button onclick="downloadIcon(32)">下载 32x32</button>
        <button onclick="downloadIcon(48)">下载 48x48</button>
        <button onclick="downloadIcon(128)">下载 128x128</button>
        <button onclick="downloadAll()">下载全部</button>
    </div>
    
    <p><strong>下载后请将文件重命名为：</strong></p>
    <ul style="text-align: left; display: inline-block;">
        <li>icon16.png</li>
        <li>icon32.png</li>
        <li>icon48.png</li>
        <li>icon128.png</li>
    </ul>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制图标
        function drawIcon() {
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 128, 128);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 128, 128);
            
            // 绘制网络图标
            ctx.strokeStyle = 'white';
            ctx.fillStyle = 'white';
            ctx.lineWidth = 4;
            
            // 绘制节点
            const nodes = [
                {x: 32, y: 32}, {x: 96, y: 32},
                {x: 64, y: 64}, {x: 32, y: 96}, {x: 96, y: 96}
            ];
            
            // 绘制连接线
            ctx.beginPath();
            ctx.moveTo(32, 32);
            ctx.lineTo(64, 64);
            ctx.lineTo(96, 32);
            ctx.moveTo(64, 64);
            ctx.lineTo(32, 96);
            ctx.moveTo(64, 64);
            ctx.lineTo(96, 96);
            ctx.stroke();
            
            // 绘制节点圆圈
            nodes.forEach(node => {
                ctx.beginPath();
                ctx.arc(node.x, node.y, 8, 0, 2 * Math.PI);
                ctx.fill();
            });
        }
        
        function downloadIcon(size) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = size;
            tempCanvas.height = size;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(canvas, 0, 0, size, size);
            
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = tempCanvas.toDataURL();
            link.click();
        }
        
        function downloadAll() {
            [16, 32, 48, 128].forEach((size, index) => {
                setTimeout(() => downloadIcon(size), index * 500);
            });
        }
        
        // 初始化绘制
        drawIcon();
    </script>
</body>
</html>
