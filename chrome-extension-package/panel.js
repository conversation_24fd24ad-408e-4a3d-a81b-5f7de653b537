class DevToolsPanel {
    constructor() {
        this.requests = [];
        this.domains = new Map();
        this.monitoringStartTime = null; // 添加监控开始时间
        this.isIndependentMode = false; // 是否为独立模式
        this.currentTabId = null;
        this.currentTabInfo = null;
        this.isAnalyzing = false;
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0,
            totalResponseTime: 0
        };

        this.checkIndependentMode();
        this.initializeElements();
        this.bindEvents();
        this.loadData();
        this.startDataRefresh();
    }

    initializeElements() {
        // 统计元素
        this.totalRequestsEl = document.getElementById('totalRequests');
        this.uniqueDomainsEl = document.getElementById('uniqueDomains');
        this.totalSizeEl = document.getElementById('totalSize');
        this.avgResponseTimeEl = document.getElementById('avgResponseTime');

        // 状态元素
        this.connectionStatus = document.getElementById('connectionStatus');
        this.lastUpdate = document.getElementById('lastUpdate');

        // 控制按钮
        this.refreshBtn = document.getElementById('refreshBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.exportBtn = document.getElementById('exportBtn');

        // 标签页
        this.tabBtns = document.querySelectorAll('.tab-btn');
        this.tabPanels = document.querySelectorAll('.tab-panel');

        // 内容容器
        this.requestsTableBody = document.getElementById('requestsTableBody');
        this.domainsGrid = document.getElementById('domainsGrid');
    }

    bindEvents() {
        // 控制按钮事件
        this.refreshBtn.addEventListener('click', () => this.loadData());
        this.clearBtn.addEventListener('click', () => this.clearData());
        this.exportBtn.addEventListener('click', () => this.exportData());

        // 标签页切换
        this.tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // 监听来自background script的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'networkRequest') {
                this.handleNetworkRequest(message.data);
            }
        });
    }

    switchTab(tabName) {
        // 更新标签按钮状态
        this.tabBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });
        
        // 更新面板显示
        this.tabPanels.forEach(panel => {
            panel.classList.toggle('active', panel.id === `${tabName}-tab`);
        });
        
        // 根据标签页加载相应内容
        switch (tabName) {
            case 'requests':
                this.updateRequestsTable();
                break;
            case 'domains':
                this.updateDomainsGrid();
                break;
            case 'timeline':
                this.updateTimelineChart();
                break;
            case 'charts':
                this.updateCharts();
                break;
        }
    }

    async checkIndependentMode() {
        // 检查URL参数是否为独立模式
        const urlParams = new URLSearchParams(window.location.search);
        this.isIndependentMode = urlParams.get('independent') === 'true';

        if (this.isIndependentMode) {
            console.log('独立面板模式启动');
            await this.initializeIndependentMode();
        }
    }

    async initializeIndependentMode() {
        try {
            // 从存储中恢复分析状态
            const data = await chrome.storage.local.get([
                'independentPanelMode', 'currentTabId', 'currentTabInfo',
                'isAnalyzing', 'monitoringStartTime'
            ]);

            if (data.independentPanelMode) {
                this.currentTabId = data.currentTabId;
                this.currentTabInfo = data.currentTabInfo;
                this.isAnalyzing = data.isAnalyzing;
                this.monitoringStartTime = data.monitoringStartTime;

                console.log('恢复分析状态:', {
                    tabId: this.currentTabId,
                    isAnalyzing: this.isAnalyzing,
                    startTime: this.monitoringStartTime
                });

                // 如果之前在分析，重新启动监控
                if (this.isAnalyzing && this.currentTabId) {
                    await this.restartMonitoring();
                }
            }
        } catch (error) {
            console.error('初始化独立模式失败:', error);
        }
    }

    async restartMonitoring() {
        try {
            // 通知background script重新开始监控
            await chrome.runtime.sendMessage({
                type: 'startAnalysis',
                tabId: this.currentTabId,
                url: this.currentTabInfo?.url || ''
            });

            console.log('重新启动网络监控');
                this.updateConnectionStatus('🟢 监控中', 'connected');
        } catch (error) {
            console.error('重新启动监控失败:', error);
            this.updateConnectionStatus('🔴 连接失败', 'error');
        }
    }

    updateConnectionStatus(text, status) {
        if (this.connectionStatus) {
            this.connectionStatus.textContent = text;
            this.connectionStatus.className = `status-indicator ${status}`;
        }
    }

    updateLastUpdateTime() {
        if (this.lastUpdate) {
            const now = new Date();
            this.lastUpdate.textContent = `最后更新: ${now.toLocaleTimeString()}`;
        }
    }

    async loadData() {
        try {
            // 从storage加载数据
            const data = await chrome.storage.local.get(['domains', 'packets', 'stats']);

            if (data.packets) {
                this.requests = data.packets;
            }

            if (data.domains) {
                this.domains = new Map(Object.entries(data.domains));
            }

            if (data.stats) {
                this.stats = data.stats;
            }

            this.updateAllViews();
            this.updateLastUpdateTime();
            this.updateConnectionStatus('🟢 数据已同步', 'connected');

        } catch (error) {
            console.error('加载数据失败:', error);
            this.updateConnectionStatus('🔴 数据加载失败', 'error');
        }
    }

    updateAllViews() {
        this.updateStats();
        this.updateRequestsTable();
        this.updateDomainsGrid();
    }

    updateStats() {
        this.totalRequestsEl.textContent = this.stats.totalRequests;
        this.uniqueDomainsEl.textContent = this.stats.uniqueDomains;
        this.totalSizeEl.textContent = this.formatSize(this.stats.totalSize);
        
        const avgTime = this.stats.totalRequests > 0 
            ? Math.round(this.stats.totalResponseTime / this.stats.totalRequests)
            : 0;
        this.avgResponseTimeEl.textContent = avgTime + 'ms';
    }

    updateRequestsTable() {
        if (this.requests.length === 0) {
            this.requestsTableBody.innerHTML = '<tr><td colspan="7" class="empty-message">暂无请求数据</td></tr>';
            return;
        }

        const rows = this.requests.slice(0, 100).map(request => {
            const url = new URL(request.url);
            const shortUrl = url.pathname + url.search;

            // 计算相对时间（毫秒）
            const relativeTime = request.relativeTime !== undefined ?
                request.relativeTime :
                (this.monitoringStartTime ? request.timestamp - this.monitoringStartTime : 0);

            return `
                <tr>
                    <td><span class="method-badge method-${request.method}">${request.method}</span></td>
                    <td title="${request.url}">${this.truncateText(shortUrl, 50)}</td>
                    <td><span class="status-badge status-${Math.floor(request.statusCode / 100)}xx">${request.statusCode}</span></td>
                    <td>${request.type || 'unknown'}</td>
                    <td>${this.formatSize(request.size)}</td>
                    <td>${request.responseTime}ms</td>
                    <td>+${relativeTime}ms</td>
                </tr>
            `;
        }).join('');

        this.requestsTableBody.innerHTML = rows;
    }

    updateDomainsGrid() {
        if (this.domains.size === 0) {
            this.domainsGrid.innerHTML = '<div class="empty-message">暂无域名数据</div>';
            return;
        }

        const sortedDomains = Array.from(this.domains.entries())
            .sort((a, b) => b[1].count - a[1].count);

        const cards = sortedDomains.map(([domain, data]) => `
            <div class="domain-card">
                <div class="domain-name">${domain}</div>
                <div class="domain-stats">
                    <div class="domain-stat">
                        <div class="domain-stat-value">${data.count}</div>
                        <div class="domain-stat-label">请求数</div>
                    </div>
                    <div class="domain-stat">
                        <div class="domain-stat-value">${this.formatSize(data.totalSize)}</div>
                        <div class="domain-stat-label">数据量</div>
                    </div>
                    <div class="domain-stat">
                        <div class="domain-stat-value">${data.avgTime}ms</div>
                        <div class="domain-stat-label">平均时间</div>
                    </div>
                </div>
            </div>
        `).join('');

        this.domainsGrid.innerHTML = cards;
    }

    updateTimelineChart() {
        const canvas = document.getElementById('timelineChart');
        const ctx = canvas.getContext('2d');
        
        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        if (this.requests.length === 0) {
            ctx.fillStyle = '#999';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }

        // 绘制时间线图表
        this.drawTimelineChart(ctx, canvas);
    }

    drawTimelineChart(ctx, canvas) {
        const padding = 40;
        const chartWidth = canvas.width - 2 * padding;
        const chartHeight = canvas.height - 2 * padding;
        
        // 获取时间范围
        const times = this.requests.map(r => r.timestamp).sort((a, b) => a - b);
        const minTime = times[0];
        const maxTime = times[times.length - 1];
        const timeRange = maxTime - minTime || 1;
        
        // 绘制坐标轴
        ctx.strokeStyle = '#e1e5e9';
        ctx.lineWidth = 1;
        
        // X轴
        ctx.beginPath();
        ctx.moveTo(padding, canvas.height - padding);
        ctx.lineTo(canvas.width - padding, canvas.height - padding);
        ctx.stroke();
        
        // Y轴
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, canvas.height - padding);
        ctx.stroke();
        
        // 绘制数据点
        this.requests.forEach(request => {
            const x = padding + ((request.timestamp - minTime) / timeRange) * chartWidth;
            const y = canvas.height - padding - (request.responseTime / 1000) * chartHeight;
            
            // 根据状态码设置颜色
            if (request.statusCode >= 200 && request.statusCode < 300) {
                ctx.fillStyle = '#28a745';
            } else if (request.statusCode >= 300 && request.statusCode < 400) {
                ctx.fillStyle = '#17a2b8';
            } else if (request.statusCode >= 400 && request.statusCode < 500) {
                ctx.fillStyle = '#ffc107';
            } else {
                ctx.fillStyle = '#dc3545';
            }
            
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();
        });
        
        // 添加标签
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('时间', canvas.width / 2, canvas.height - 10);
        
        ctx.save();
        ctx.translate(15, canvas.height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText('响应时间 (ms)', 0, 0);
        ctx.restore();
    }

    updateCharts() {
        this.updateMethodChart();
        this.updateStatusChart();
        this.updateTypeChart();
        this.updateDomainChart();
    }

    updateMethodChart() {
        const canvas = document.getElementById('methodChart');
        const ctx = canvas.getContext('2d');
        
        const methods = {};
        this.requests.forEach(request => {
            methods[request.method] = (methods[request.method] || 0) + 1;
        });
        
        this.drawPieChart(ctx, canvas, methods, {
            'GET': '#28a745',
            'POST': '#007bff',
            'PUT': '#ffc107',
            'DELETE': '#dc3545'
        });
    }

    updateStatusChart() {
        const canvas = document.getElementById('statusChart');
        const ctx = canvas.getContext('2d');
        
        const statuses = {};
        this.requests.forEach(request => {
            const statusGroup = Math.floor(request.statusCode / 100) + 'xx';
            statuses[statusGroup] = (statuses[statusGroup] || 0) + 1;
        });
        
        this.drawPieChart(ctx, canvas, statuses, {
            '2xx': '#28a745',
            '3xx': '#17a2b8',
            '4xx': '#ffc107',
            '5xx': '#dc3545'
        });
    }

    updateTypeChart() {
        const canvas = document.getElementById('typeChart');
        const ctx = canvas.getContext('2d');
        
        const types = {};
        this.requests.forEach(request => {
            types[request.type || 'unknown'] = (types[request.type || 'unknown'] || 0) + 1;
        });
        
        this.drawPieChart(ctx, canvas, types);
    }

    updateDomainChart() {
        const canvas = document.getElementById('domainChart');
        const ctx = canvas.getContext('2d');
        
        const domainCounts = {};
        Array.from(this.domains.entries()).forEach(([domain, data]) => {
            domainCounts[domain] = data.count;
        });
        
        this.drawBarChart(ctx, canvas, domainCounts);
    }

    drawPieChart(ctx, canvas, data, colors = {}) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        const entries = Object.entries(data);
        if (entries.length === 0) {
            ctx.fillStyle = '#999';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }
        
        const total = entries.reduce((sum, [, value]) => sum + value, 0);
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;
        
        let currentAngle = 0;
        const defaultColors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
        
        entries.forEach(([key, value], index) => {
            const sliceAngle = (value / total) * 2 * Math.PI;
            
            ctx.fillStyle = colors[key] || defaultColors[index % defaultColors.length];
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fill();
            
            // 添加标签
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
            const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
            
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(key, labelX, labelY);
            
            currentAngle += sliceAngle;
        });
    }

    drawBarChart(ctx, canvas, data) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        const entries = Object.entries(data).slice(0, 5); // 只显示前5个
        if (entries.length === 0) {
            ctx.fillStyle = '#999';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }
        
        const padding = 30;
        const chartWidth = canvas.width - 2 * padding;
        const chartHeight = canvas.height - 2 * padding;
        const barWidth = chartWidth / entries.length;
        const maxValue = Math.max(...entries.map(([, value]) => value));
        
        entries.forEach(([key, value], index) => {
            const barHeight = (value / maxValue) * chartHeight;
            const x = padding + index * barWidth;
            const y = canvas.height - padding - barHeight;
            
            ctx.fillStyle = '#667eea';
            ctx.fillRect(x + 5, y, barWidth - 10, barHeight);
            
            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(this.truncateText(key, 10), x + barWidth / 2, canvas.height - 5);
            ctx.fillText(value.toString(), x + barWidth / 2, y - 5);
        });
    }

    async clearData() {
        this.requests = [];
        this.domains.clear();
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0,
            totalResponseTime: 0
        };
        
        await chrome.storage.local.clear();
        this.updateAllViews();
    }

    async exportData() {
        const data = {
            timestamp: new Date().toISOString(),
            stats: this.stats,
            domains: Object.fromEntries(this.domains),
            requests: this.requests
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `network-analysis-detailed-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }

    handleNetworkRequest(request) {
        try {
            const domain = new URL(request.url).hostname;

            // 更新域名统计
            if (this.domains.has(domain)) {
                const domainData = this.domains.get(domain);
                domainData.count++;
                domainData.totalSize += request.size;
                domainData.totalTime += request.responseTime;
                domainData.avgTime = Math.round(domainData.totalTime / domainData.count);
            } else {
                this.domains.set(domain, {
                    count: 1,
                    totalSize: request.size,
                    totalTime: request.responseTime,
                    avgTime: request.responseTime
                });
            }

            // 添加到请求列表
            this.requests.unshift(request);
            if (this.requests.length > 100) {
                this.requests = this.requests.slice(0, 100);
            }

            // 更新统计
            this.stats.totalRequests++;
            this.stats.uniqueDomains = this.domains.size;
            this.stats.totalSize += request.size;
            this.stats.totalResponseTime += request.responseTime;

            // 实时更新界面
            this.updateAllViews();
            this.updateLastUpdateTime();
            this.updateConnectionStatus('🟢 实时监控中', 'connected');

            console.log('独立面板收到新请求:', request.url, '+' + request.relativeTime + 'ms');

        } catch (error) {
            console.error('处理网络请求失败:', error);
        }
    }

    startDataRefresh() {
        // 每3秒刷新一次数据（独立模式下更频繁）
        const refreshInterval = this.isIndependentMode ? 3000 : 5000;
        setInterval(() => {
            this.loadData();
        }, refreshInterval);
    }

    formatSize(bytes) {
        if (bytes === 0) return '0B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
}

// 初始化面板
document.addEventListener('DOMContentLoaded', () => {
    new DevToolsPanel();
});
