class DevToolsPanel {
    constructor() {
        this.requests = [];
        this.domains = new Map();
        this.monitoringStartTime = null; // 添加监控开始时间
        this.isIndependentMode = false; // 是否为独立模式
        this.currentTabId = null;
        this.currentTabInfo = null;
        this.isAnalyzing = false;
        this.currentPacketFilter = 'all'; // 当前数据包过滤器
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0,
            totalResponseTime: 0
        };

        this.checkIndependentMode();
        this.initializeElements();
        this.bindEvents();
        this.loadData();
        this.startDataRefresh();
    }

    initializeElements() {
        // 统计元素
        this.totalRequestsEl = document.getElementById('totalRequests');
        this.uniqueDomainsEl = document.getElementById('uniqueDomains');
        this.totalSizeEl = document.getElementById('totalSize');
        this.avgResponseTimeEl = document.getElementById('avgResponseTime');

        // 状态元素
        this.connectionStatus = document.getElementById('connectionStatus');
        this.lastUpdate = document.getElementById('lastUpdate');

        // 控制按钮
        this.refreshBtn = document.getElementById('refreshBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.exportBtn = document.getElementById('exportBtn');

        // 标签页
        this.tabBtns = document.querySelectorAll('.tab-btn');
        this.tabPanels = document.querySelectorAll('.tab-panel');

        // 内容容器
        this.requestsTableBody = document.getElementById('requestsTableBody');
        this.domainsGrid = document.getElementById('domainsGrid');
        this.packetsList = document.getElementById('packetsList');

        // 过滤按钮
        this.filterBtns = document.querySelectorAll('.filter-btn');
    }

    bindEvents() {
        // 控制按钮事件
        this.refreshBtn.addEventListener('click', () => this.loadData());
        this.clearBtn.addEventListener('click', () => this.clearData());
        this.exportBtn.addEventListener('click', () => this.exportData());

        // 标签页切换
        this.tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // 数据包过滤按钮
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const filter = e.target.dataset.filter;
                this.setPacketFilter(filter);
            });
        });

        // 监听来自background script的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'networkRequest') {
                this.handleNetworkRequest(message.data);
            }
        });
    }

    switchTab(tabName) {
        // 更新标签按钮状态
        this.tabBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });
        
        // 更新面板显示
        this.tabPanels.forEach(panel => {
            panel.classList.toggle('active', panel.id === `${tabName}-tab`);
        });
        
        // 根据标签页加载相应内容
        switch (tabName) {
            case 'requests':
                this.updateRequestsTable();
                break;
            case 'domains':
                this.updateDomainsGrid();
                break;
            case 'timeline':
                this.updateTimelineChart();
                break;
            case 'charts':
                this.updateCharts();
                break;
        }
    }

    async checkIndependentMode() {
        // 检查URL参数是否为独立模式
        const urlParams = new URLSearchParams(window.location.search);
        this.isIndependentMode = urlParams.get('independent') === 'true';

        if (this.isIndependentMode) {
            console.log('独立面板模式启动');
            await this.initializeIndependentMode();
        }
    }

    async initializeIndependentMode() {
        try {
            // 从存储中恢复分析状态
            const data = await chrome.storage.local.get([
                'independentPanelMode', 'currentTabId', 'currentTabInfo',
                'isAnalyzing', 'monitoringStartTime'
            ]);

            if (data.independentPanelMode) {
                this.currentTabId = data.currentTabId;
                this.currentTabInfo = data.currentTabInfo;
                this.isAnalyzing = data.isAnalyzing;
                this.monitoringStartTime = data.monitoringStartTime;

                console.log('恢复分析状态:', {
                    tabId: this.currentTabId,
                    isAnalyzing: this.isAnalyzing,
                    startTime: this.monitoringStartTime
                });

                // 如果之前在分析，重新启动监控
                if (this.isAnalyzing && this.currentTabId) {
                    await this.restartMonitoring();
                }
            }
        } catch (error) {
            console.error('初始化独立模式失败:', error);
        }
    }

    async restartMonitoring() {
        try {
            // 通知background script重新开始监控
            await chrome.runtime.sendMessage({
                type: 'startAnalysis',
                tabId: this.currentTabId,
                url: this.currentTabInfo?.url || ''
            });

            console.log('重新启动网络监控');
                this.updateConnectionStatus('🟢 监控中', 'connected');
        } catch (error) {
            console.error('重新启动监控失败:', error);
            this.updateConnectionStatus('🔴 连接失败', 'error');
        }
    }

    updateConnectionStatus(text, status) {
        if (this.connectionStatus) {
            this.connectionStatus.textContent = text;
            this.connectionStatus.className = `status-indicator ${status}`;
        }
    }

    updateLastUpdateTime() {
        if (this.lastUpdate) {
            const now = new Date();
            this.lastUpdate.textContent = `最后更新: ${now.toLocaleTimeString()}`;
        }
    }

    async loadData() {
        try {
            // 从storage加载数据
            const data = await chrome.storage.local.get(['domains', 'packets', 'stats']);

            if (data.packets) {
                this.requests = data.packets;
            }

            if (data.domains) {
                this.domains = new Map(Object.entries(data.domains));
            }

            if (data.stats) {
                this.stats = data.stats;
            }

            this.updateAllViews();
            this.updateLastUpdateTime();
            this.updateConnectionStatus('🟢 数据已同步', 'connected');

        } catch (error) {
            console.error('加载数据失败:', error);
            this.updateConnectionStatus('🔴 数据加载失败', 'error');
        }
    }

    updateAllViews() {
        this.updateStats();
        this.updateRequestsTable();
        this.updateDomainsGrid();
        this.updatePacketsList();
        this.updateTimelineChart();
        this.updateCharts();
    }

    updateStats() {
        this.totalRequestsEl.textContent = this.stats.totalRequests;
        this.uniqueDomainsEl.textContent = this.stats.uniqueDomains;
        this.totalSizeEl.textContent = this.formatSize(this.stats.totalSize);
        
        const avgTime = this.stats.totalRequests > 0 
            ? Math.round(this.stats.totalResponseTime / this.stats.totalRequests)
            : 0;
        this.avgResponseTimeEl.textContent = avgTime + 'ms';
    }

    updateRequestsTable() {
        if (this.requests.length === 0) {
            this.requestsTableBody.innerHTML = '<tr><td colspan="7" class="empty-message">暂无请求数据</td></tr>';
            return;
        }

        const rows = this.requests.slice(0, 100).map(request => {
            const url = new URL(request.url);
            const shortUrl = url.pathname + url.search;

            // 计算相对时间（毫秒）
            const relativeTime = request.relativeTime !== undefined ?
                request.relativeTime :
                (this.monitoringStartTime ? request.timestamp - this.monitoringStartTime : 0);

            return `
                <tr>
                    <td><span class="method-badge method-${request.method}">${request.method}</span></td>
                    <td title="${request.url}">${this.truncateText(shortUrl, 50)}</td>
                    <td><span class="status-badge status-${Math.floor(request.statusCode / 100)}xx">${request.statusCode}</span></td>
                    <td>${request.type || 'unknown'}</td>
                    <td>${this.formatSize(request.size)}</td>
                    <td>${request.responseTime}ms</td>
                    <td>+${relativeTime}ms</td>
                </tr>
            `;
        }).join('');

        this.requestsTableBody.innerHTML = rows;
    }

    updateDomainsGrid() {
        if (this.domains.size === 0) {
            this.domainsGrid.innerHTML = '<div class="empty-message">暂无域名数据</div>';
            return;
        }

        const sortedDomains = Array.from(this.domains.entries())
            .sort((a, b) => b[1].count - a[1].count);

        const cards = sortedDomains.map(([domain, data]) => `
            <div class="domain-card">
                <div class="domain-name">${domain}</div>
                <div class="domain-stats">
                    <div class="domain-stat">
                        <div class="domain-stat-value">${data.count}</div>
                        <div class="domain-stat-label">请求数</div>
                    </div>
                    <div class="domain-stat">
                        <div class="domain-stat-value">${this.formatSize(data.totalSize)}</div>
                        <div class="domain-stat-label">数据量</div>
                    </div>
                    <div class="domain-stat">
                        <div class="domain-stat-value">${data.avgTime}ms</div>
                        <div class="domain-stat-label">平均时间</div>
                    </div>
                </div>
            </div>
        `).join('');

        this.domainsGrid.innerHTML = cards;
    }

    setPacketFilter(filter) {
        this.currentPacketFilter = filter;

        // 更新过滤按钮状态
        this.filterBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });

        // 更新数据包列表
        this.updatePacketsList();
    }

    updatePacketsList() {
        if (!this.packetsList) return;

        const filteredPackets = this.getFilteredPackets();

        if (filteredPackets.length === 0) {
            this.packetsList.innerHTML = '<div class="empty-message">暂无重要数据包</div>';
            return;
        }

        this.packetsList.innerHTML = filteredPackets.slice(0, 20).map(packet => {
            const typeClass = this.getPacketTypeClass(packet);
            const priorityClass = `priority-${packet.priority || 'low'}`;
            const domain = new URL(packet.url).hostname;

            return `
                <div class="packet-item ${typeClass}">
                    <div class="packet-header">
                        <div>
                            <span class="packet-method method-${packet.method}">${packet.method}</span>
                            <span class="packet-priority ${priorityClass}">${this.getPriorityText(packet.priority)}</span>
                        </div>
                        <div class="packet-time">+${packet.relativeTime || 0}ms</div>
                    </div>
                    <div class="packet-url" title="${packet.url}">
                        ${this.truncateUrl(packet.url, 80)}
                    </div>
                    <div class="packet-details">
                        状态: ${packet.statusCode} | 大小: ${this.formatSize(packet.size)} | 时间: ${packet.responseTime}ms | 域名: ${domain}
                    </div>
                    ${packet.sensitiveData ? `
                        <div class="packet-sensitive">
                            🔒 敏感信息: ${this.truncateText(packet.sensitiveData, 60)}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    getFilteredPackets() {
        if (this.currentPacketFilter === 'all') {
            return this.requests;
        }

        return this.requests.filter(packet => {
            switch (this.currentPacketFilter) {
                case 'auth':
                    return this.isAuthRequest(packet);
                case 'api':
                    return this.isApiRequest(packet);
                case 'cookies':
                    return this.hasCookies(packet);
                case 'sensitive':
                    return packet.sensitiveData;
                default:
                    return true;
            }
        });
    }

    isAuthRequest(packet) {
        const url = packet.url.toLowerCase();
        const authKeywords = ['login', 'auth', 'signin', 'signup', 'register', 'oauth', 'token'];
        return authKeywords.some(keyword => url.includes(keyword)) ||
               packet.method === 'POST' && url.includes('auth');
    }

    isApiRequest(packet) {
        const url = packet.url.toLowerCase();
        return url.includes('/api/') ||
               url.includes('/v1/') ||
               url.includes('/v2/') ||
               packet.type === 'xmlhttprequest';
    }

    hasCookies(packet) {
        return packet.url.includes('cookie') ||
               packet.url.includes('session') ||
               packet.type === 'xmlhttprequest';
    }

    getPacketTypeClass(packet) {
        if (this.isAuthRequest(packet)) return 'auth-packet';
        if (this.isApiRequest(packet)) return 'api-packet';
        if (this.hasCookies(packet)) return 'cookie-packet';
        if (packet.sensitiveData) return 'sensitive-packet';
        return 'normal-packet';
    }

    getPriorityText(priority) {
        switch (priority) {
            case 'high': return '高';
            case 'medium': return '中';
            case 'low': return '低';
            default: return '普通';
        }
    }

    truncateUrl(url, maxLength) {
        if (url.length <= maxLength) return url;
        return url.substring(0, maxLength) + '...';
    }

    updateTimelineChart() {
        const canvas = document.getElementById('timelineChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        if (this.requests.length === 0) {
            ctx.fillStyle = '#999';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }

        // 绘制时间线图表
        this.drawTimelineChart(ctx, canvas);

        // 添加鼠标悬停事件
        this.addTimelineHoverEvents(canvas);
    }

    drawTimelineChart(ctx, canvas) {
        const padding = 40;
        const chartWidth = canvas.width - 2 * padding;
        const chartHeight = canvas.height - 2 * padding;

        // 获取时间范围
        const times = this.requests.map(r => r.relativeTime || 0).sort((a, b) => a - b);
        const minTime = times[0] || 0;
        const maxTime = times[times.length - 1] || 1000;
        const timeRange = maxTime - minTime || 1000;

        // 获取响应时间范围
        const responseTimes = this.requests.map(r => r.responseTime).sort((a, b) => a - b);
        const maxResponseTime = responseTimes[responseTimes.length - 1] || 1000;

        // 获取所有域名并分配颜色
        const domains = [...new Set(this.requests.map(r => {
            try {
                return new URL(r.url).hostname;
            } catch {
                return 'unknown';
            }
        }))];

        const domainColors = this.generateDomainColors(domains);

        // 绘制坐标轴
        ctx.strokeStyle = '#e1e5e9';
        ctx.lineWidth = 1;

        // X轴
        ctx.beginPath();
        ctx.moveTo(padding, canvas.height - padding);
        ctx.lineTo(canvas.width - padding, canvas.height - padding);
        ctx.stroke();

        // Y轴
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, canvas.height - padding);
        ctx.stroke();

        // 存储数据点信息，用于鼠标悬停
        this.timelineDataPoints = [];

        // 绘制数据点
        this.requests.forEach(request => {
            const relativeTime = request.relativeTime || 0;
            const x = padding + ((relativeTime - minTime) / timeRange) * chartWidth;
            const y = canvas.height - padding - (request.responseTime / maxResponseTime) * (chartHeight * 0.8);

            let domain;
            try {
                domain = new URL(request.url).hostname;
            } catch {
                domain = 'unknown';
            }

            // 根据域名设置颜色
            ctx.fillStyle = domainColors[domain] || '#999';

            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();

            // 添加边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 1;
            ctx.stroke();

            // 存储数据点信息
            this.timelineDataPoints.push({
                x: x,
                y: y,
                radius: 4,
                request: request,
                domain: domain
            });
        });

        // 添加标签
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('相对时间 (ms)', canvas.width / 2, canvas.height - 10);

        ctx.save();
        ctx.translate(15, canvas.height / 2);
        ctx.rotate(-Math.PI / 2);
        ctx.fillText('响应时间 (ms)', 0, 0);
        ctx.restore();

        // 绘制域名图例
        this.drawDomainLegend(ctx, canvas, domainColors, domains);
    }

    generateDomainColors(domains) {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ];

        const domainColors = {};
        domains.forEach((domain, index) => {
            domainColors[domain] = colors[index % colors.length];
        });

        return domainColors;
    }

    drawDomainLegend(ctx, canvas, domainColors, domains) {
        const legendX = canvas.width - 200;
        const legendY = 20;
        const itemHeight = 20;

        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.fillRect(legendX - 10, legendY - 10, 190, domains.length * itemHeight + 20);

        ctx.strokeStyle = '#ddd';
        ctx.strokeRect(legendX - 10, legendY - 10, 190, domains.length * itemHeight + 20);

        domains.slice(0, 8).forEach((domain, index) => {
            const y = legendY + index * itemHeight;

            // 绘制颜色圆点
            ctx.fillStyle = domainColors[domain];
            ctx.beginPath();
            ctx.arc(legendX, y + 8, 6, 0, 2 * Math.PI);
            ctx.fill();

            // 绘制域名文本
            ctx.fillStyle = '#333';
            ctx.font = '11px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(this.truncateText(domain, 20), legendX + 15, y + 12);
        });

        if (domains.length > 8) {
            ctx.fillStyle = '#666';
            ctx.font = '10px Arial';
            ctx.fillText(`+${domains.length - 8} more...`, legendX, legendY + 8 * itemHeight + 10);
        }
    }

    addTimelineHoverEvents(canvas) {
        // 创建tooltip元素
        let tooltip = document.getElementById('timeline-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'timeline-tooltip';
            tooltip.style.cssText = `
                position: absolute;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                pointer-events: none;
                z-index: 1000;
                display: none;
                max-width: 300px;
                word-wrap: break-word;
            `;
            document.body.appendChild(tooltip);
        }

        // 鼠标移动事件
        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            let hoveredPoint = null;

            // 检查是否悬停在数据点上
            if (this.timelineDataPoints) {
                for (const point of this.timelineDataPoints) {
                    const distance = Math.sqrt(
                        Math.pow(mouseX - point.x, 2) + Math.pow(mouseY - point.y, 2)
                    );

                    if (distance <= point.radius + 2) {
                        hoveredPoint = point;
                        break;
                    }
                }
            }

            if (hoveredPoint) {
                // 显示tooltip
                const request = hoveredPoint.request;
                const domain = hoveredPoint.domain;

                tooltip.innerHTML = `
                    <div><strong>域名:</strong> ${domain}</div>
                    <div><strong>方法:</strong> ${request.method}</div>
                    <div><strong>状态:</strong> ${request.statusCode}</div>
                    <div><strong>响应时间:</strong> ${request.responseTime}ms</div>
                    <div><strong>相对时间:</strong> +${request.relativeTime || 0}ms</div>
                    <div><strong>大小:</strong> ${this.formatSize(request.size)}</div>
                    <div><strong>URL:</strong> ${this.truncateText(request.url, 40)}</div>
                `;

                tooltip.style.display = 'block';
                tooltip.style.left = (e.clientX + 10) + 'px';
                tooltip.style.top = (e.clientY - 10) + 'px';

                // 改变鼠标样式
                canvas.style.cursor = 'pointer';
            } else {
                // 隐藏tooltip
                tooltip.style.display = 'none';
                canvas.style.cursor = 'default';
            }
        });

        // 鼠标离开事件
        canvas.addEventListener('mouseleave', () => {
            tooltip.style.display = 'none';
            canvas.style.cursor = 'default';
        });
    }

    updateCharts() {
        this.updateMethodChart();
        this.updateStatusChart();
        this.updateTypeChart();
        this.updateDomainChart();
    }

    updateMethodChart() {
        const canvas = document.getElementById('methodChart');
        const ctx = canvas.getContext('2d');
        
        const methods = {};
        this.requests.forEach(request => {
            methods[request.method] = (methods[request.method] || 0) + 1;
        });
        
        this.drawPieChart(ctx, canvas, methods, {
            'GET': '#28a745',
            'POST': '#007bff',
            'PUT': '#ffc107',
            'DELETE': '#dc3545'
        });
    }

    updateStatusChart() {
        const canvas = document.getElementById('statusChart');
        const ctx = canvas.getContext('2d');
        
        const statuses = {};
        this.requests.forEach(request => {
            const statusGroup = Math.floor(request.statusCode / 100) + 'xx';
            statuses[statusGroup] = (statuses[statusGroup] || 0) + 1;
        });
        
        this.drawPieChart(ctx, canvas, statuses, {
            '2xx': '#28a745',
            '3xx': '#17a2b8',
            '4xx': '#ffc107',
            '5xx': '#dc3545'
        });
    }

    updateTypeChart() {
        const canvas = document.getElementById('typeChart');
        const ctx = canvas.getContext('2d');
        
        const types = {};
        this.requests.forEach(request => {
            types[request.type || 'unknown'] = (types[request.type || 'unknown'] || 0) + 1;
        });
        
        this.drawPieChart(ctx, canvas, types);
    }

    updateDomainChart() {
        const canvas = document.getElementById('domainChart');
        const ctx = canvas.getContext('2d');
        
        const domainCounts = {};
        Array.from(this.domains.entries()).forEach(([domain, data]) => {
            domainCounts[domain] = data.count;
        });
        
        this.drawBarChart(ctx, canvas, domainCounts);
    }

    drawPieChart(ctx, canvas, data, colors = {}) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        const entries = Object.entries(data);
        if (entries.length === 0) {
            ctx.fillStyle = '#999';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }
        
        const total = entries.reduce((sum, [, value]) => sum + value, 0);
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 20;
        
        let currentAngle = 0;
        const defaultColors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
        
        entries.forEach(([key, value], index) => {
            const sliceAngle = (value / total) * 2 * Math.PI;
            
            ctx.fillStyle = colors[key] || defaultColors[index % defaultColors.length];
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fill();
            
            // 添加标签
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
            const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
            
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(key, labelX, labelY);
            
            currentAngle += sliceAngle;
        });
    }

    drawBarChart(ctx, canvas, data) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        const entries = Object.entries(data).slice(0, 5); // 只显示前5个
        if (entries.length === 0) {
            ctx.fillStyle = '#999';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
            return;
        }
        
        const padding = 30;
        const chartWidth = canvas.width - 2 * padding;
        const chartHeight = canvas.height - 2 * padding;
        const barWidth = chartWidth / entries.length;
        const maxValue = Math.max(...entries.map(([, value]) => value));
        
        entries.forEach(([key, value], index) => {
            const barHeight = (value / maxValue) * chartHeight;
            const x = padding + index * barWidth;
            const y = canvas.height - padding - barHeight;
            
            ctx.fillStyle = '#667eea';
            ctx.fillRect(x + 5, y, barWidth - 10, barHeight);
            
            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(this.truncateText(key, 10), x + barWidth / 2, canvas.height - 5);
            ctx.fillText(value.toString(), x + barWidth / 2, y - 5);
        });
    }

    async clearData() {
        this.requests = [];
        this.domains.clear();
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0,
            totalResponseTime: 0
        };
        
        await chrome.storage.local.clear();
        this.updateAllViews();
    }

    async exportData() {
        const data = {
            timestamp: new Date().toISOString(),
            stats: this.stats,
            domains: Object.fromEntries(this.domains),
            requests: this.requests
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `network-analysis-detailed-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
    }

    handleNetworkRequest(request) {
        try {
            const domain = new URL(request.url).hostname;

            // 更新域名统计
            if (this.domains.has(domain)) {
                const domainData = this.domains.get(domain);
                domainData.count++;
                domainData.totalSize += request.size;
                domainData.totalTime += request.responseTime;
                domainData.avgTime = Math.round(domainData.totalTime / domainData.count);
            } else {
                this.domains.set(domain, {
                    count: 1,
                    totalSize: request.size,
                    totalTime: request.responseTime,
                    avgTime: request.responseTime
                });
            }

            // 添加到请求列表
            this.requests.unshift(request);
            if (this.requests.length > 100) {
                this.requests = this.requests.slice(0, 100);
            }

            // 更新统计
            this.stats.totalRequests++;
            this.stats.uniqueDomains = this.domains.size;
            this.stats.totalSize += request.size;
            this.stats.totalResponseTime += request.responseTime;

            // 实时更新界面
            this.updateAllViews();
            this.updateLastUpdateTime();
            this.updateConnectionStatus('🟢 实时监控中', 'connected');

            console.log('独立面板收到新请求:', request.url, '+' + request.relativeTime + 'ms');

        } catch (error) {
            console.error('处理网络请求失败:', error);
        }
    }

    startDataRefresh() {
        // 每3秒刷新一次数据（独立模式下更频繁）
        const refreshInterval = this.isIndependentMode ? 3000 : 5000;
        setInterval(() => {
            this.loadData();
        }, refreshInterval);
    }

    formatSize(bytes) {
        if (bytes === 0) return '0B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
}

// 初始化面板
document.addEventListener('DOMContentLoaded', () => {
    new DevToolsPanel();
});
