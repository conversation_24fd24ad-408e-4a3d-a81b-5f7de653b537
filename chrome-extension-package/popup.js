class NetworkAnalyzer {
    constructor() {
        this.isAnalyzing = false;
        this.currentTabId = null;
        this.currentTabInfo = null;
        this.domains = new Map();
        this.packets = [];
        this.currentFilter = 'all';
        this.monitoringStartTime = null; // 添加监控开始时间
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0
        };

        this.initializeElements();
        this.bindEvents();
        this.loadCurrentTab();
        this.loadStoredData();
    }

    initializeElements() {
        // 当前标签页信息
        this.currentTabTitle = document.getElementById('currentTabTitle');
        this.currentTabUrl = document.getElementById('currentTabUrl');

        // 控制按钮
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.btnText = document.getElementById('btnText');
        this.btnLoader = document.getElementById('btnLoader');
        this.manualAnalyzeBtn = document.getElementById('manualAnalyzeBtn');
        this.urlInput = document.getElementById('urlInput');

        // 其他按钮
        this.clearBtn = document.getElementById('clearBtn');
        this.exportBtn = document.getElementById('exportBtn');
        this.devtoolsBtn = document.getElementById('devtoolsBtn');
        this.statusText = document.getElementById('statusText');
        this.statusIndicator = document.getElementById('statusIndicator');

        // 统计元素
        this.totalRequestsEl = document.getElementById('totalRequests');
        this.uniqueDomainsEl = document.getElementById('uniqueDomains');
        this.totalSizeEl = document.getElementById('totalSize');

        // 列表元素
        this.domainsList = document.getElementById('domainsList');
        this.packetsList = document.getElementById('packetsList');

        // 过滤器标签
        this.filterTabs = document.querySelectorAll('.filter-tab');
    }

    bindEvents() {
        // 分析当前页面按钮
        this.analyzeBtn.addEventListener('click', () => this.toggleAnalysis());

        // 手动分析按钮
        if (this.manualAnalyzeBtn) {
            this.manualAnalyzeBtn.addEventListener('click', () => this.analyzeManualUrl());
        }

        // 控制按钮
        this.clearBtn.addEventListener('click', () => this.clearData());
        this.exportBtn.addEventListener('click', () => this.exportReport());
        this.devtoolsBtn.addEventListener('click', () => this.openDevtools());

        // URL输入框回车
        if (this.urlInput) {
            this.urlInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.analyzeManualUrl();
                }
            });
        }

        // 过滤器标签
        this.filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // 监听来自background script的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'networkRequest') {
                this.handleNetworkRequest(message.data);
            } else if (message.type === 'monitoringStarted') {
                this.monitoringStartTime = message.data.startTime;
            }
        });
    }

    async loadCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTabInfo = tab;
            this.currentTabId = tab.id;

            // 更新UI显示当前标签页信息
            this.currentTabTitle.textContent = tab.title || '未知页面';
            this.currentTabUrl.textContent = tab.url || 'chrome://';

            // 如果是特殊页面，显示提示
            if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.btnText.textContent = '无法分析此页面';
                this.analyzeBtn.disabled = true;
            } else {
                this.btnText.textContent = '分析当前页面';
                this.analyzeBtn.disabled = false;
            }
        } catch (error) {
            console.error('获取当前标签页失败:', error);
            this.currentTabTitle.textContent = '获取失败';
            this.currentTabUrl.textContent = '请刷新插件';
        }
    }

    setFilter(filter) {
        this.currentFilter = filter;

        // 更新标签状态
        this.filterTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.filter === filter);
        });

        // 重新渲染数据包列表
        this.updatePacketsList();
    }

    async toggleAnalysis() {
        if (this.isAnalyzing) {
            await this.stopAnalysis();
        } else {
            await this.startAnalysis();
        }
    }

    async startAnalysis() {
        if (!this.currentTabInfo || this.analyzeBtn.disabled) {
            this.showError('无法分析当前页面');
            return;
        }

        this.isAnalyzing = true;
        this.updateUI();
        this.setStatus('正在启动分析...', 'analyzing');

        try {
            // 清除之前的数据
            this.clearData();

            // 设置监控开始时间
            this.monitoringStartTime = Date.now();

            // 通知background script开始监听
            await chrome.runtime.sendMessage({
                type: 'startAnalysis',
                tabId: this.currentTabId,
                url: this.currentTabInfo.url
            });

            this.setStatus('正在分析网络流量...', 'analyzing');

            // 开始模拟数据更新（用于演示）
            this.startSimulation();

        } catch (error) {
            console.error('启动分析失败:', error);
            this.showError('启动分析失败: ' + error.message);
            this.isAnalyzing = false;
            this.updateUI();
        }
    }

    async analyzeManualUrl() {
        const url = this.urlInput.value.trim();
        if (!url) {
            this.showError('请输入有效的URL');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showError('请输入有效的URL格式');
            return;
        }

        this.isAnalyzing = true;
        this.updateUI();
        this.setStatus('正在启动分析...', 'analyzing');

        try {
            // 清除之前的数据
            this.clearData();

            // 设置监控开始时间
            this.monitoringStartTime = Date.now();

            // 通知background script开始监听
            await chrome.runtime.sendMessage({
                type: 'startAnalysis',
                tabId: this.currentTabId,
                url: url
            });

            // 导航到目标URL
            await chrome.tabs.update(this.currentTabId, { url: url });

            this.setStatus('正在分析网络流量...', 'analyzing');

            // 开始模拟数据更新（用于演示）
            this.startSimulation();

        } catch (error) {
            console.error('启动分析失败:', error);
            this.showError('启动分析失败: ' + error.message);
            this.isAnalyzing = false;
            this.updateUI();
        }
    }

    async stopAnalysis() {
        this.isAnalyzing = false;
        this.updateUI();
        this.setStatus('分析已停止', 'ready');

        try {
            await chrome.runtime.sendMessage({
                type: 'stopAnalysis',
                tabId: this.currentTabId
            });
        } catch (error) {
            console.error('停止分析失败:', error);
        }

        this.stopSimulation();
    }

    startSimulation() {
        // 模拟网络请求数据（用于演示）
        this.simulationInterval = setInterval(() => {
            if (!this.isAnalyzing) return;

            const mockRequests = this.generateMockRequests();
            mockRequests.forEach(request => {
                this.handleNetworkRequest(request);
            });
        }, 2000);
    }

    stopSimulation() {
        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }
    }

    generateMockRequests() {
        const mockData = [
            // 身份认证相关
            {
                domain: 'accounts.google.com',
                path: '/oauth/token',
                method: 'POST',
                type: 'auth',
                priority: 'high',
                sensitiveData: 'OAuth Token: eyJhbGciOiJSUzI1NiIs...'
            },
            {
                domain: 'login.microsoftonline.com',
                path: '/common/oauth2/v2.0/token',
                method: 'POST',
                type: 'auth',
                priority: 'high',
                sensitiveData: 'Access Token: Bearer abc123...'
            },
            // API请求
            {
                domain: 'api.github.com',
                path: '/user/repos',
                method: 'GET',
                type: 'api',
                priority: 'medium',
                sensitiveData: 'API Key: ghp_xxxxxxxxxxxx'
            },
            {
                domain: 'graph.facebook.com',
                path: '/me/friends',
                method: 'GET',
                type: 'api',
                priority: 'medium',
                sensitiveData: 'User ID: 1234567890'
            },
            // Cookie相关
            {
                domain: 'www.example.com',
                path: '/login',
                method: 'POST',
                type: 'cookies',
                priority: 'high',
                sensitiveData: 'Session: JSESSIONID=ABC123; user_id=12345'
            },
            // 普通请求
            {
                domain: 'cdn.jsdelivr.net',
                path: '/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
                method: 'GET',
                type: 'resource',
                priority: 'low'
            },
            {
                domain: 'fonts.googleapis.com',
                path: '/css2?family=Roboto:wght@300;400;500&display=swap',
                method: 'GET',
                type: 'resource',
                priority: 'low'
            }
        ];

        const requests = [];
        const count = Math.floor(Math.random() * 3) + 1;

        for (let i = 0; i < count; i++) {
            const mockItem = mockData[Math.floor(Math.random() * mockData.length)];
            const statusCode = mockItem.type === 'auth' ? 200 : [200, 201, 304, 404][Math.floor(Math.random() * 4)];
            const size = Math.floor(Math.random() * 50000) + 1000;
            const responseTime = Math.floor(Math.random() * 500) + 50;

            const timestamp = Date.now();
            const relativeTime = this.monitoringStartTime ? timestamp - this.monitoringStartTime : 0;

            requests.push({
                url: `https://${mockItem.domain}${mockItem.path}?t=${timestamp}`,
                method: mockItem.method,
                statusCode: statusCode,
                size: size,
                responseTime: responseTime,
                timestamp: timestamp,
                relativeTime: relativeTime,
                type: mockItem.type,
                priority: mockItem.priority,
                sensitiveData: mockItem.sensitiveData,
                domain: mockItem.domain
            });
        }

        return requests;
    }

    handleNetworkRequest(request) {
        try {
            const domain = new URL(request.url).hostname;

            // 更新域名统计
            if (this.domains.has(domain)) {
                const domainData = this.domains.get(domain);
                domainData.count++;
                domainData.totalSize += request.size;
                domainData.totalTime += request.responseTime;
                domainData.avgTime = Math.round(domainData.totalTime / domainData.count);
            } else {
                this.domains.set(domain, {
                    count: 1,
                    totalSize: request.size,
                    totalTime: request.responseTime,
                    avgTime: request.responseTime
                });
            }

            // 添加到数据包列表
            this.packets.unshift(request);
            if (this.packets.length > 50) {
                this.packets = this.packets.slice(0, 50);
            }

            // 更新统计
            this.stats.totalRequests++;
            this.stats.uniqueDomains = this.domains.size;
            this.stats.totalSize += request.size;

            // 更新UI
            this.updateStats();
            this.updateDomainsList();
            this.updatePacketsList();

            // 保存数据
            this.saveData();

        } catch (error) {
            console.error('处理网络请求失败:', error);
        }
    }

    updateUI() {
        if (this.isAnalyzing) {
            this.btnText.textContent = '停止分析';
            this.btnLoader.classList.remove('hidden');
            this.analyzeBtn.classList.add('analyzing');
        } else {
            if (this.analyzeBtn.disabled) {
                this.btnText.textContent = '无法分析此页面';
            } else {
                this.btnText.textContent = '分析当前页面';
            }
            this.btnLoader.classList.add('hidden');
            this.analyzeBtn.classList.remove('analyzing');
        }
    }

    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    updateStats() {
        this.totalRequestsEl.textContent = this.stats.totalRequests;
        this.uniqueDomainsEl.textContent = this.stats.uniqueDomains;
        this.totalSizeEl.textContent = this.formatSize(this.stats.totalSize);
    }

    updateDomainsList() {
        if (this.domains.size === 0) {
            this.domainsList.innerHTML = '<div class="empty-state"><p>开始分析后，这里将显示访问的域名</p></div>';
            return;
        }

        const sortedDomains = Array.from(this.domains.entries())
            .sort((a, b) => b[1].count - a[1].count);

        this.domainsList.innerHTML = sortedDomains.map(([domain, data]) => `
            <div class="domain-item">
                <div class="domain-name" title="${domain}">${domain}</div>
                <div class="domain-count">${data.count}</div>
            </div>
        `).join('');
    }

    updatePacketsList() {
        if (this.packets.length === 0) {
            this.packetsList.innerHTML = '<div class="empty-state"><p>开始分析后，这里将显示重要的网络请求详情</p></div>';
            return;
        }

        // 根据当前过滤器过滤数据包
        let filteredPackets = this.packets;
        if (this.currentFilter !== 'all') {
            filteredPackets = this.packets.filter(packet => {
                switch (this.currentFilter) {
                    case 'auth':
                        return packet.type === 'auth' || this.isAuthRelated(packet);
                    case 'api':
                        return packet.type === 'api' || this.isApiRequest(packet);
                    case 'cookies':
                        return packet.type === 'cookies' || this.hasCookies(packet);
                    default:
                        return true;
                }
            });
        }

        // 按优先级排序
        filteredPackets.sort((a, b) => {
            const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
            return (priorityOrder[b.priority] || 1) - (priorityOrder[a.priority] || 1);
        });

        if (filteredPackets.length === 0) {
            this.packetsList.innerHTML = '<div class="empty-state"><p>没有符合条件的数据包</p></div>';
            return;
        }

        this.packetsList.innerHTML = filteredPackets.slice(0, 15).map(packet => {
            const typeClass = packet.type || 'normal';
            const priorityClass = `priority-${packet.priority || 'low'}`;

            // 计算相对时间（毫秒）
            const relativeTime = packet.relativeTime !== undefined ?
                packet.relativeTime :
                (this.monitoringStartTime ? packet.timestamp - this.monitoringStartTime : 0);

            return `
                <div class="packet-item ${typeClass}">
                    <div class="packet-header">
                        <div>
                            <span class="packet-method method-${packet.method}">${packet.method}</span>
                            <span class="packet-priority ${priorityClass}">${this.getPriorityText(packet.priority)}</span>
                        </div>
                        <div class="packet-time">+${relativeTime}ms</div>
                    </div>
                    <div class="packet-url" title="${packet.url}">
                        ${this.truncateUrl(packet.url, 60)}
                    </div>
                    <div class="packet-details">
                        状态: ${packet.statusCode} | 大小: ${this.formatSize(packet.size)} | 时间: ${packet.responseTime}ms
                        ${packet.domain ? ` | 域名: ${packet.domain}` : ''}
                    </div>
                    ${packet.sensitiveData ? `
                        <div class="packet-sensitive">
                            🔒 敏感信息: ${this.truncateText(packet.sensitiveData, 50)}
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    isAuthRelated(packet) {
        const authKeywords = ['login', 'auth', 'oauth', 'token', 'session', 'signin', 'sso'];
        const url = packet.url.toLowerCase();
        return authKeywords.some(keyword => url.includes(keyword));
    }

    isApiRequest(packet) {
        const apiKeywords = ['api', 'graphql', 'rest', 'endpoint'];
        const url = packet.url.toLowerCase();
        return apiKeywords.some(keyword => url.includes(keyword)) ||
               packet.url.includes('/api/') ||
               packet.method !== 'GET';
    }

    hasCookies(packet) {
        // 简化检测，实际应该检查请求头
        return packet.sensitiveData && packet.sensitiveData.toLowerCase().includes('cookie');
    }

    getPriorityText(priority) {
        switch (priority) {
            case 'high': return '高';
            case 'medium': return '中';
            case 'low': return '低';
            default: return '普通';
        }
    }

    clearData() {
        this.domains.clear();
        this.packets = [];
        this.stats = {
            totalRequests: 0,
            uniqueDomains: 0,
            totalSize: 0
        };

        this.updateStats();
        this.updateDomainsList();
        this.updatePacketsList();
        this.saveData();
    }

    async exportReport() {
        const report = {
            timestamp: new Date().toISOString(),
            url: this.urlInput.value,
            stats: this.stats,
            domains: Object.fromEntries(this.domains),
            packets: this.packets
        };

        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        await chrome.downloads.download({
            url: url,
            filename: `network-analysis-${Date.now()}.json`
        });
    }

    async openDevtools() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            await chrome.tabs.update(tab.id, { active: true });
            // 注意：Chrome扩展无法直接打开DevTools，需要用户手动打开
            this.showInfo('请按F12打开开发者工具查看详细信息');
        } catch (error) {
            console.error('打开DevTools失败:', error);
        }
    }

    setStatus(text, type = 'ready') {
        this.statusText.textContent = text;
        this.statusIndicator.className = `status-indicator ${type}`;
    }

    showError(message) {
        this.setStatus(message, 'error');
        setTimeout(() => {
            this.setStatus('就绪', 'ready');
        }, 3000);
    }

    showInfo(message) {
        this.setStatus(message, 'ready');
        setTimeout(() => {
            this.setStatus('就绪', 'ready');
        }, 3000);
    }

    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    formatSize(bytes) {
        if (bytes === 0) return '0B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + sizes[i];
    }

    truncateUrl(url, maxLength = 40) {
        if (url.length <= maxLength) return url;
        return url.substring(0, maxLength) + '...';
    }

    async saveData() {
        try {
            await chrome.storage.local.set({
                domains: Object.fromEntries(this.domains),
                packets: this.packets,
                stats: this.stats
            });
        } catch (error) {
            console.error('保存数据失败:', error);
        }
    }

    async loadStoredData() {
        try {
            const data = await chrome.storage.local.get(['domains', 'packets', 'stats']);

            if (data.domains) {
                this.domains = new Map(Object.entries(data.domains));
            }
            if (data.packets) {
                this.packets = data.packets;
            }
            if (data.stats) {
                this.stats = data.stats;
            }

            this.updateStats();
            this.updateDomainsList();
            this.updatePacketsList();
        } catch (error) {
            console.error('加载数据失败:', error);
        }
    }
}

// 初始化分析器
document.addEventListener('DOMContentLoaded', () => {
    new NetworkAnalyzer();
});
