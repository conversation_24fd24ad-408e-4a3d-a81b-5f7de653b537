* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    height: 100vh;
    overflow: hidden;
}

.panel-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.panel-header h1 {
    font-size: 18px;
    font-weight: 600;
}

.header-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.stats-overview {
    display: flex;
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #e1e5e9;
    gap: 30px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #667eea;
}

.tabs {
    display: flex;
    background: white;
    border-bottom: 1px solid #e1e5e9;
    padding: 0 20px;
}

.tab-btn {
    padding: 12px 20px;
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background: #f8f9fa;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f8f9fa;
}

.tab-content {
    flex: 1;
    overflow: hidden;
}

.tab-panel {
    height: 100%;
    overflow: auto;
    display: none;
    padding: 20px;
}

.tab-panel.active {
    display: block;
}

/* 请求表格 */
.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.requests-table {
    width: 100%;
    border-collapse: collapse;
}

.requests-table th {
    background: #f8f9fa;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #666;
    border-bottom: 1px solid #e1e5e9;
}

.requests-table td {
    padding: 10px 12px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
}

.requests-table tr:hover {
    background: #f8f9fa;
}

.method-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
}

.method-GET { background: #28a745; color: white; }
.method-POST { background: #007bff; color: white; }
.method-PUT { background: #ffc107; color: black; }
.method-DELETE { background: #dc3545; color: white; }

.status-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
}

.status-2xx { background: #28a745; color: white; }
.status-3xx { background: #17a2b8; color: white; }
.status-4xx { background: #ffc107; color: black; }
.status-5xx { background: #dc3545; color: white; }

/* 无响应状态特殊样式 */
.status-0xx {
    background: #6c757d !important;
    color: white !important;
    animation: pulse 2s infinite;
    position: relative;
}

.status-0xx::after {
    content: '⚠️';
    margin-left: 4px;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* 域名网格 */
.domains-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.domain-card {
    background: white;
    border-radius: 8px;
    padding: 25px;
    min-height: 180px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.domain-card:hover {
    transform: translateY(-2px);
}

.domain-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    word-break: break-all;
    line-height: 1.4;
    min-height: 60px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: visible;
}

.domain-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.domain-stat {
    text-align: center;
}

.domain-stat-value {
    font-size: 18px;
    font-weight: 600;
    color: #667eea;
}

.domain-stat-label {
    font-size: 11px;
    color: #666;
    margin-top: 2px;
}

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.chart-item {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-item h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

/* 时间线 */
.timeline-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 空状态 */
.empty-message {
    text-align: center;
    color: #999;
    font-size: 14px;
    padding: 40px;
}

/* 滚动条样式 */
.tab-panel::-webkit-scrollbar {
    width: 6px;
}

.tab-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.tab-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.tab-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 数据包过滤器 */
.packets-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 0 20px;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.filter-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

/* 数据包列表 */
.packets-list {
    max-height: 500px;
    overflow-y: auto;
    padding: 0 20px;
}

.packet-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #ddd;
    transition: transform 0.2s ease;
}

.packet-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.packet-item.auth-packet {
    border-left-color: #dc3545;
}

.packet-item.api-packet {
    border-left-color: #007bff;
}

.packet-item.cookie-packet {
    border-left-color: #ffc107;
}

.packet-item.sensitive-packet {
    border-left-color: #e83e8c;
}

.packet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.packet-method {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 8px;
}

.method-GET { background: #28a745; color: white; }
.method-POST { background: #007bff; color: white; }
.method-PUT { background: #ffc107; color: black; }
.method-DELETE { background: #dc3545; color: white; }

.packet-priority {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
}

.priority-high { background: #dc3545; color: white; }
.priority-medium { background: #ffc107; color: black; }
.priority-low { background: #6c757d; color: white; }

.packet-time {
    font-family: monospace;
    font-size: 12px;
    color: #666;
    font-weight: bold;
}

.packet-url {
    font-family: monospace;
    font-size: 12px;
    color: #333;
    margin-bottom: 5px;
    word-break: break-all;
}

.packet-details {
    font-size: 11px;
    color: #666;
    margin-bottom: 5px;
}

.packet-sensitive {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 11px;
    color: #856404;
    margin-top: 5px;
}

/* 状态信息 */
.status-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-right: 20px;
}

.status-indicator {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    background: #f8f9fa;
}

.status-indicator.connected {
    background: #d4edda;
    color: #155724;
}

.status-indicator.error {
    background: #f8d7da;
    color: #721c24;
}

.last-update {
    font-size: 11px;
    color: #666;
}

/* 时间线图表增强 */
.timeline-container {
    position: relative;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#timelineChart {
    cursor: crosshair;
}
