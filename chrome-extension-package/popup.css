* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 600px;
}

.container {
    width: 500px;
    min-height: 700px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-text {
    text-align: left;
    flex: 1;
}

.always-on-top-btn {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 8px;
    color: #667eea;
    font-size: 16px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}

.always-on-top-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.05);
}

.always-on-top-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
}

.header h1 {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 5px;
}

.header p {
    font-size: 12px;
    color: #666;
}

.url-section {
    margin-bottom: 20px;
}

.current-tab-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 12px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.tab-icon {
    font-size: 20px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
}

.tab-details {
    flex: 1;
    min-width: 0;
}

.tab-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tab-url {
    font-size: 11px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.manual-input {
    display: flex;
    gap: 8px;
    align-items: center;
}

.input-group {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
}

#urlInput {
    flex: 1;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

#urlInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-primary {
    padding: 12px 16px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 80px;
    justify-content: center;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loader {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none;
}

.quick-urls {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.quick-urls span {
    color: #666;
    font-weight: 500;
}

.quick-btn {
    padding: 4px 8px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    padding: 15px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #f0f0f0;
}

.stat-number {
    font-size: 20px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 11px;
    color: #666;
    font-weight: 500;
}

.domains-section, .packets-section {
    margin-bottom: 20px;
}

.domains-section h3, .packets-section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.domains-list {
    max-height: 180px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.packets-list {
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.domain-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.2s ease;
}

.domain-item:hover {
    background: #f8f9fa;
}

.domain-item:last-child {
    border-bottom: none;
}

.domain-name {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.domain-count {
    font-size: 11px;
    background: #667eea;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.packet-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f8f9fa;
    font-size: 11px;
    transition: background 0.2s ease;
}

.packet-item:hover {
    background: #f8f9fa;
}

.packet-item:last-child {
    border-bottom: none;
}

.packet-method {
    display: inline-block;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 600;
    font-size: 10px;
    margin-right: 6px;
}

.method-GET { background: #28a745; color: white; }
.method-POST { background: #007bff; color: white; }
.method-PUT { background: #ffc107; color: black; }
.method-DELETE { background: #dc3545; color: white; }

.packet-url {
    color: #333;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.packet-details {
    color: #666;
    font-size: 10px;
}

.packets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.packets-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.filter-tabs {
    display: flex;
    gap: 4px;
}

.filter-tab {
    padding: 4px 8px;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #666;
}

.filter-tab:hover {
    background: #e9ecef;
}

.filter-tab.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.packet-item {
    padding: 10px 12px;
    border-bottom: 1px solid #f8f9fa;
    font-size: 11px;
    transition: background 0.2s ease;
}

.packet-item:hover {
    background: #f8f9fa;
}

.packet-item:last-child {
    border-bottom: none;
}

.packet-item.important {
    border-left: 3px solid #ff6b6b;
    background: rgba(255, 107, 107, 0.05);
}

.packet-item.auth {
    border-left: 3px solid #4ecdc4;
    background: rgba(78, 205, 196, 0.05);
}

.packet-item.api {
    border-left: 3px solid #45b7d1;
    background: rgba(69, 183, 209, 0.05);
}

.packet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.packet-method {
    display: inline-block;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 600;
    font-size: 9px;
    margin-right: 6px;
}

.packet-priority {
    display: inline-block;
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 9px;
    font-weight: 600;
}

.priority-high {
    background: #ff6b6b;
    color: white;
}

.priority-medium {
    background: #ffa726;
    color: white;
}

.priority-low {
    background: #66bb6a;
    color: white;
}

.packet-url {
    color: #333;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 500;
}

.packet-details {
    color: #666;
    font-size: 10px;
    margin-bottom: 4px;
}

.packet-sensitive {
    background: rgba(255, 193, 7, 0.1);
    padding: 4px 6px;
    border-radius: 4px;
    font-size: 9px;
    color: #856404;
    margin-top: 4px;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.empty-state {
    padding: 20px;
    text-align: center;
    color: #999;
    font-size: 12px;
}

.controls {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.btn-secondary {
    flex: 1;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 11px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.analyzing {
    background: #ffc107;
}

.status-indicator.error {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 滚动条样式 */
.domains-list::-webkit-scrollbar,
.packets-list::-webkit-scrollbar {
    width: 4px;
}

.domains-list::-webkit-scrollbar-track,
.packets-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.domains-list::-webkit-scrollbar-thumb,
.packets-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.domains-list::-webkit-scrollbar-thumb:hover,
.packets-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
