<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络流量分析器 - 详细面板</title>
    <link rel="stylesheet" href="panel.css">
</head>
<body>
    <div class="panel-container">
        <header class="panel-header">
            <h1>🌐 网络流量详细分析</h1>
            <div class="header-controls">
                <button id="refreshBtn" class="control-btn">刷新</button>
                <button id="clearBtn" class="control-btn">清除</button>
                <button id="exportBtn" class="control-btn">导出</button>
            </div>
        </header>

        <div class="panel-content">
            <div class="stats-overview">
                <div class="stat-item">
                    <span class="stat-label">总请求数:</span>
                    <span class="stat-value" id="totalRequests">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">唯一域名:</span>
                    <span class="stat-value" id="uniqueDomains">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">总数据量:</span>
                    <span class="stat-value" id="totalSize">0KB</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均响应时间:</span>
                    <span class="stat-value" id="avgResponseTime">0ms</span>
                </div>
            </div>

            <div class="tabs">
                <button class="tab-btn active" data-tab="requests">请求列表</button>
                <button class="tab-btn" data-tab="domains">域名分析</button>
                <button class="tab-btn" data-tab="timeline">时间线</button>
                <button class="tab-btn" data-tab="charts">图表分析</button>
            </div>

            <div class="tab-content">
                <!-- 请求列表 -->
                <div id="requests-tab" class="tab-panel active">
                    <div class="table-container">
                        <table class="requests-table">
                            <thead>
                                <tr>
                                    <th>方法</th>
                                    <th>URL</th>
                                    <th>状态</th>
                                    <th>类型</th>
                                    <th>大小</th>
                                    <th>时间</th>
                                    <th>时间戳</th>
                                </tr>
                            </thead>
                            <tbody id="requestsTableBody">
                                <tr>
                                    <td colspan="7" class="empty-message">暂无请求数据</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 域名分析 -->
                <div id="domains-tab" class="tab-panel">
                    <div class="domains-grid" id="domainsGrid">
                        <div class="empty-message">暂无域名数据</div>
                    </div>
                </div>

                <!-- 时间线 -->
                <div id="timeline-tab" class="tab-panel">
                    <div class="timeline-container">
                        <canvas id="timelineChart" width="800" height="400"></canvas>
                    </div>
                </div>

                <!-- 图表分析 -->
                <div id="charts-tab" class="tab-panel">
                    <div class="charts-grid">
                        <div class="chart-item">
                            <h3>请求方法分布</h3>
                            <canvas id="methodChart" width="300" height="200"></canvas>
                        </div>
                        <div class="chart-item">
                            <h3>响应状态分布</h3>
                            <canvas id="statusChart" width="300" height="200"></canvas>
                        </div>
                        <div class="chart-item">
                            <h3>资源类型分布</h3>
                            <canvas id="typeChart" width="300" height="200"></canvas>
                        </div>
                        <div class="chart-item">
                            <h3>域名请求量</h3>
                            <canvas id="domainChart" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="panel.js"></script>
</body>
</html>
