<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动生成Chrome插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .preview-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(5px);
        }
        .preview-item h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }
        canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: white;
        }
        .download-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            margin-top: 10px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }
        .download-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .main-download {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 20px 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .main-download:hover {
            background: #218838;
            transform: translateY(-2px);
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            text-align: left;
            backdrop-filter: blur(5px);
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            border-radius: 8px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Chrome插件图标生成器</h1>
        <p>为网络流量分析器插件自动生成所需的PNG图标文件</p>
        
        <div class="preview-grid">
            <div class="preview-item">
                <h3>16×16</h3>
                <canvas id="canvas16" width="16" height="16"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(16)">下载</button>
            </div>
            <div class="preview-item">
                <h3>32×32</h3>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(32)">下载</button>
            </div>
            <div class="preview-item">
                <h3>48×48</h3>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(48)">下载</button>
            </div>
            <div class="preview-item">
                <h3>128×128</h3>
                <canvas id="canvas128" width="128" height="128"></canvas>
                <br>
                <button class="download-btn" onclick="downloadIcon(128)">下载</button>
            </div>
        </div>
        
        <div>
            <button class="main-download" onclick="downloadAll()">🚀 一键下载全部图标</button>
            <button class="main-download" onclick="generateBase64()">📋 生成Base64代码</button>
        </div>
        
        <div id="status" class="status">
            <p id="statusText">正在下载图标文件...</p>
        </div>
        
        <div class="instructions">
            <h3>📋 安装步骤：</h3>
            <div class="step">
                <strong>1.</strong> 点击"一键下载全部图标"按钮，下载所有PNG文件
            </div>
            <div class="step">
                <strong>2.</strong> 将下载的文件重命名为：
                <ul>
                    <li><code>icon16.png</code></li>
                    <li><code>icon32.png</code></li>
                    <li><code>icon48.png</code></li>
                    <li><code>icon128.png</code></li>
                </ul>
            </div>
            <div class="step">
                <strong>3.</strong> 将这些文件复制到插件的 <code>icons/</code> 文件夹中
            </div>
            <div class="step">
                <strong>4.</strong> 打开Chrome浏览器，访问 <code>chrome://extensions/</code>
            </div>
            <div class="step">
                <strong>5.</strong> 开启"开发者模式"，点击"加载已解压的扩展程序"
            </div>
            <div class="step">
                <strong>6.</strong> 选择 <code>chrome-extension-package</code> 文件夹，完成安装
            </div>
        </div>
    </div>

    <script>
        // 绘制网络图标
        function drawNetworkIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 计算缩放比例
            const scale = size / 128;
            
            // 设置绘制样式
            ctx.strokeStyle = 'white';
            ctx.fillStyle = 'white';
            ctx.lineWidth = Math.max(1, 3 * scale);
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 定义节点位置
            const nodes = [
                {x: 32 * scale, y: 32 * scale},   // 左上
                {x: 96 * scale, y: 32 * scale},   // 右上
                {x: 64 * scale, y: 64 * scale},   // 中心
                {x: 32 * scale, y: 96 * scale},   // 左下
                {x: 96 * scale, y: 96 * scale}    // 右下
            ];
            
            // 绘制连接线
            ctx.beginPath();
            // 从左上到中心
            ctx.moveTo(nodes[0].x, nodes[0].y);
            ctx.lineTo(nodes[2].x, nodes[2].y);
            // 从右上到中心
            ctx.moveTo(nodes[1].x, nodes[1].y);
            ctx.lineTo(nodes[2].x, nodes[2].y);
            // 从中心到左下
            ctx.moveTo(nodes[2].x, nodes[2].y);
            ctx.lineTo(nodes[3].x, nodes[3].y);
            // 从中心到右下
            ctx.moveTo(nodes[2].x, nodes[2].y);
            ctx.lineTo(nodes[4].x, nodes[4].y);
            ctx.stroke();
            
            // 绘制节点圆圈
            const nodeRadius = Math.max(2, 6 * scale);
            nodes.forEach(node => {
                ctx.beginPath();
                ctx.arc(node.x, node.y, nodeRadius, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            // 添加小的装饰元素（数据包）
            if (size >= 32) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                const packetSize = Math.max(1, 2 * scale);
                
                // 在连接线上添加小方块表示数据包
                const packets = [
                    {x: 48 * scale, y: 48 * scale},
                    {x: 80 * scale, y: 48 * scale},
                    {x: 48 * scale, y: 80 * scale},
                    {x: 80 * scale, y: 80 * scale}
                ];
                
                packets.forEach(packet => {
                    ctx.fillRect(packet.x - packetSize, packet.y - packetSize, 
                               packetSize * 2, packetSize * 2);
                });
            }
        }
        
        // 下载单个图标
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 下载所有图标
        function downloadAll() {
            const status = document.getElementById('status');
            const statusText = document.getElementById('statusText');
            
            status.style.display = 'block';
            statusText.textContent = '正在下载图标文件...';
            
            const sizes = [16, 32, 48, 128];
            let downloaded = 0;
            
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    downloadIcon(size);
                    downloaded++;
                    statusText.textContent = `已下载 ${downloaded}/${sizes.length} 个图标文件`;
                    
                    if (downloaded === sizes.length) {
                        setTimeout(() => {
                            statusText.textContent = '✅ 所有图标文件下载完成！请按照说明重命名并放入icons文件夹';
                        }, 500);
                    }
                }, index * 500);
            });
        }
        
        // 生成Base64编码（用于直接嵌入代码）
        function generateBase64() {
            const sizes = [16, 32, 48, 128];
            let base64Data = {};
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                base64Data[`icon${size}`] = canvas.toDataURL('image/png');
            });
            
            // 创建下载链接
            const dataStr = JSON.stringify(base64Data, null, 2);
            const blob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.download = 'icons_base64.json';
            link.href = url;
            link.click();
            
            URL.revokeObjectURL(url);
        }
        
        // 初始化所有画布
        function initializeCanvases() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                drawNetworkIcon(canvas, size);
            });
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initializeCanvases();
            console.log('🎨 图标生成器已就绪');
        });
    </script>
</body>
</html>
