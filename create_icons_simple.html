<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成Chrome插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            text-align: center;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #667eea;
            margin-bottom: 20px;
        }
        .canvas-container {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .buttons {
            margin: 20px 0;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: left;
        }
        .step {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-left: 4px solid #667eea;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Chrome插件图标生成器</h1>
        <p>为网络流量分析器插件生成所需的图标文件</p>
        
        <div class="canvas-container">
            <canvas id="canvas16" width="16" height="16" title="16x16"></canvas>
            <canvas id="canvas32" width="32" height="32" title="32x32"></canvas>
            <canvas id="canvas48" width="48" height="48" title="48x48"></canvas>
            <canvas id="canvas128" width="128" height="128" title="128x128"></canvas>
        </div>
        
        <div class="buttons">
            <button onclick="downloadIcon(16)">下载 16x16</button>
            <button onclick="downloadIcon(32)">下载 32x32</button>
            <button onclick="downloadIcon(48)">下载 48x48</button>
            <button onclick="downloadIcon(128)">下载 128x128</button>
            <button onclick="downloadAll()" style="background: #28a745;">一键下载全部</button>
        </div>
        
        <div class="instructions">
            <h3>📋 安装步骤：</h3>
            <div class="step">
                <strong>1.</strong> 点击上面的按钮下载所有图标文件
            </div>
            <div class="step">
                <strong>2.</strong> 将下载的文件重命名为：
                <ul>
                    <li>icon16.png</li>
                    <li>icon32.png</li>
                    <li>icon48.png</li>
                    <li>icon128.png</li>
                </ul>
            </div>
            <div class="step">
                <strong>3.</strong> 将这些文件放入插件的 <code>icons/</code> 文件夹中
            </div>
            <div class="step">
                <strong>4.</strong> 打开Chrome浏览器，访问 <code>chrome://extensions/</code>
            </div>
            <div class="step">
                <strong>5.</strong> 开启"开发者模式"，点击"加载已解压的扩展程序"
            </div>
            <div class="step">
                <strong>6.</strong> 选择 <code>chrome-extension-package</code> 文件夹
            </div>
        </div>
    </div>

    <script>
        // 绘制图标的函数
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 计算缩放比例
            const scale = size / 128;
            
            // 设置绘制样式
            ctx.strokeStyle = 'white';
            ctx.fillStyle = 'white';
            ctx.lineWidth = Math.max(1, 3 * scale);
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            // 定义节点位置
            const nodes = [
                {x: 32 * scale, y: 32 * scale},
                {x: 96 * scale, y: 32 * scale},
                {x: 64 * scale, y: 64 * scale},
                {x: 32 * scale, y: 96 * scale},
                {x: 96 * scale, y: 96 * scale}
            ];
            
            // 绘制连接线
            ctx.beginPath();
            ctx.moveTo(nodes[0].x, nodes[0].y);
            ctx.lineTo(nodes[2].x, nodes[2].y);
            ctx.lineTo(nodes[1].x, nodes[1].y);
            ctx.moveTo(nodes[2].x, nodes[2].y);
            ctx.lineTo(nodes[3].x, nodes[3].y);
            ctx.moveTo(nodes[2].x, nodes[2].y);
            ctx.lineTo(nodes[4].x, nodes[4].y);
            ctx.stroke();
            
            // 绘制节点
            const nodeRadius = Math.max(2, 6 * scale);
            nodes.forEach(node => {
                ctx.beginPath();
                ctx.arc(node.x, node.y, nodeRadius, 0, 2 * Math.PI);
                ctx.fill();
            });
        }
        
        // 下载图标函数
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        // 下载所有图标
        function downloadAll() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    downloadIcon(size);
                }, index * 300);
            });
        }
        
        // 初始化所有画布
        function initializeCanvases() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                drawIcon(canvas, size);
            });
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', initializeCanvases);
    </script>
</body>
</html>
