# 网络流量分析器 Chrome 插件

一个功能强大的Chrome浏览器插件，用于实时分析网页访问的域名和网络流量包。

## 功能特点

### 🌐 核心功能
- **URL输入分析** - 输入目标网址，一键开始流量分析
- **实时监控** - 实时捕获和分析网络请求
- **域名提取** - 自动提取并统计访问的所有域名
- **流量统计** - 详细的数据传输量和响应时间统计

### 📊 数据展示
- **概览统计** - 总请求数、唯一域名数、传输数据量
- **域名分析** - 每个域名的访问次数和数据量
- **数据包详情** - HTTP方法、状态码、响应时间等
- **可视化图表** - 多种图表展示流量分析结果

### 🎨 界面特色
- **现代化设计** - 渐变色彩和流畅动画
- **响应式布局** - 适配不同屏幕尺寸
- **直观操作** - 简洁易用的用户界面
- **实时更新** - 数据实时刷新显示

## 安装方法

### 方法一：开发者模式安装

1. **下载插件文件**
   ```bash
   git clone <repository-url>
   cd Net-packet-Analysis
   ```

2. **生成图标文件**
   - 在浏览器中打开 `icons/create_icons.html`
   - 图标文件将自动下载
   - 将下载的 `icon16.png`, `icon32.png`, `icon48.png`, `icon128.png` 放入 `icons/` 文件夹

3. **安装到Chrome**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择插件文件夹

### 方法二：打包安装

1. **打包插件**
   - 在 `chrome://extensions/` 页面
   - 点击"打包扩展程序"
   - 选择插件文件夹
   - 生成 `.crx` 文件

2. **安装打包文件**
   - 将 `.crx` 文件拖拽到Chrome扩展页面
   - 确认安装

## 使用说明

### 基本使用

1. **启动分析**
   - 点击浏览器工具栏中的插件图标
   - 在弹出窗口中输入要分析的网址
   - 点击"开始分析"按钮

2. **查看结果**
   - 实时查看统计数据
   - 浏览访问的域名列表
   - 查看最新的数据包详情

3. **高级功能**
   - 点击"打开详细面板"查看更多分析
   - 使用"导出报告"保存分析结果
   - 使用"清除数据"重置统计

### 快速操作

- **快速URL** - 使用预设的快速选择按钮
- **键盘快捷键** - 在URL输入框按回车键开始分析
- **状态指示** - 通过颜色指示器查看分析状态

## 技术架构

### 插件组件

- **Manifest V3** - 使用最新的Chrome扩展API
- **Background Script** - 后台网络监听和数据处理
- **Content Script** - 页面内容脚本，监听客户端请求
- **Popup Interface** - 主要用户界面
- **DevTools Panel** - 开发者工具详细分析面板

### 数据流程

```
网页请求 → Background Script → 数据处理 → Storage → UI更新
         ↓
    Content Script → 客户端监听 → 补充数据
```

### 权限说明

- `activeTab` - 访问当前活动标签页
- `webRequest` - 监听网络请求
- `storage` - 本地数据存储
- `tabs` - 标签页操作
- `debugger` - 网络调试功能

## 开发说明

### 文件结构

```
Net-packet-Analysis/
├── manifest.json          # 插件配置文件
├── popup.html             # 弹窗界面
├── popup.css              # 弹窗样式
├── popup.js               # 弹窗逻辑
├── background.js          # 后台脚本
├── content.js             # 内容脚本
├── devtools.html          # 开发者工具入口
├── devtools.js            # 开发者工具脚本
├── panel.html             # 详细分析面板
├── panel.css              # 面板样式
├── panel.js               # 面板逻辑
├── icons/                 # 插件图标
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md              # 说明文档
```

### 自定义开发

1. **修改UI样式**
   - 编辑 `popup.css` 和 `panel.css`
   - 调整颜色、布局和动画效果

2. **扩展功能**
   - 在 `background.js` 中添加新的网络监听逻辑
   - 在 `popup.js` 中添加新的用户交互功能

3. **数据处理**
   - 修改数据格式和存储逻辑
   - 添加新的分析算法

## 故障排除

### 常见问题

1. **插件无法加载**
   - 检查manifest.json语法
   - 确保所有文件路径正确
   - 查看Chrome扩展页面的错误信息

2. **网络监听不工作**
   - 确认权限设置正确
   - 检查目标网站是否有特殊限制
   - 查看控制台错误信息

3. **数据不显示**
   - 检查Storage权限
   - 确认数据格式正确
   - 重新加载插件

### 调试方法

1. **查看控制台**
   - 右键插件图标 → 检查弹出式窗口
   - 在扩展页面查看背景页面

2. **网络调试**
   - 使用Chrome DevTools
   - 查看Network面板
   - 检查请求拦截情况

## 更新日志

### v2.0.0 增强版 (最新) 🚀
- ✅ 添加重要数据包分析功能（5种智能过滤器）
- ✅ 时间线图表鼠标悬停显示域名信息
- ✅ 不同域名使用不同颜色区分（15种颜色）
- ✅ 域名颜色图例显示
- ✅ 智能识别身份认证、API、Cookies、敏感数据
- ✅ 彩色边框区分不同类型的数据包

### v1.3.0 实时同步版
- ✅ 修复独立面板数据实时同步问题
- ✅ 时间显示修复（显示真实递增时间）
- ✅ 📌按钮功能正确（打开独立面板）
- ✅ 状态指示器和最后更新时间显示
- ✅ 双重数据同步机制

### v1.2.0 修正版
- ✅ 修复时间显示问题（不再显示+0ms）
- ✅ 修正📌按钮功能（打开独立面板而非跳转页面）
- ✅ 改进时间计算逻辑

### v1.1.0 优化时间显示版
- ✅ 时间显示改为相对时间格式（+XXXms）
- ✅ 域名显示框增大（400px宽度）
- ✅ 强化标签页过滤，确保只捕获当前标签页数据

### v1.0.0
- 初始版本发布
- 基本网络监听功能
- 域名分析和统计
- 现代化UI界面
- DevTools集成

## 🎯 最新功能亮点

### 📊 重要数据包分析
- **全部**：显示所有网络请求
- **身份认证**：自动识别登录、认证相关请求
- **API请求**：识别API调用和AJAX请求
- **Cookies**：分析Cookie和会话相关请求
- **敏感数据**：标识包含敏感信息的请求

### 📈 增强时间线图表
- 基于相对时间的时间线显示
- 不同域名使用不同颜色区分
- 鼠标悬停显示详细信息tooltip
- 右侧域名颜色图例
- 15种鲜明颜色循环使用

### 📌 独立分析面板
- 900x700独立窗口
- 实时数据同步
- 自动恢复分析状态
- 可以一边操作网页一边查看分析

## 📦 下载最新版本

最新版本：`网络流量分析器-Chrome插件-增强版.zip`

包含所有最新功能和修复！

## 许可证

MIT License - 详见LICENSE文件

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- Email: [<EMAIL>]

---

**注意**: 此插件仅用于合法的网络分析和学习目的，请遵守相关法律法规和网站使用条款。
