class NetworkMonitor {
    constructor() {
        this.isMonitoring = false;
        this.currentTabId = null;
        this.requestData = new Map();
        
        this.initializeListeners();
    }

    initializeListeners() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听网络请求
        chrome.webRequest.onBeforeRequest.addListener(
            (details) => this.handleBeforeRequest(details),
            { urls: ["<all_urls>"] },
            ["requestBody"]
        );

        chrome.webRequest.onSendHeaders.addListener(
            (details) => this.handleSendHeaders(details),
            { urls: ["<all_urls>"] },
            ["requestHeaders"]
        );

        chrome.webRequest.onHeadersReceived.addListener(
            (details) => this.handleHeadersReceived(details),
            { urls: ["<all_urls>"] },
            ["responseHeaders"]
        );

        chrome.webRequest.onCompleted.addListener(
            (details) => this.handleCompleted(details),
            { urls: ["<all_urls>"] }
        );

        chrome.webRequest.onErrorOccurred.addListener(
            (details) => this.handleError(details),
            { urls: ["<all_urls>"] }
        );

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (this.isMonitoring && tabId === this.currentTabId) {
                if (changeInfo.status === 'loading') {
                    this.clearRequestData();
                }
            }
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.type) {
                case 'startAnalysis':
                    await this.startMonitoring(message.tabId, message.url);
                    sendResponse({ success: true });
                    break;

                case 'stopAnalysis':
                    await this.stopMonitoring();
                    sendResponse({ success: true });
                    break;

                case 'getRequestData':
                    sendResponse({ 
                        success: true, 
                        data: Array.from(this.requestData.values()) 
                    });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown message type' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async startMonitoring(tabId, url) {
        this.isMonitoring = true;
        this.currentTabId = tabId;
        this.clearRequestData();
        
        console.log(`开始监控标签页 ${tabId} 的网络请求: ${url}`);
        
        // 通知popup监控已开始
        this.notifyPopup('monitoringStarted', { tabId, url });
    }

    async stopMonitoring() {
        this.isMonitoring = false;
        this.currentTabId = null;
        
        console.log('停止网络监控');
        
        // 通知popup监控已停止
        this.notifyPopup('monitoringStopped', {});
    }

    handleBeforeRequest(details) {
        if (!this.shouldMonitorRequest(details)) return;

        const requestData = {
            requestId: details.requestId,
            url: details.url,
            method: details.method,
            type: details.type,
            tabId: details.tabId,
            timestamp: details.timeStamp,
            requestBody: details.requestBody
        };

        this.requestData.set(details.requestId, requestData);
    }

    handleSendHeaders(details) {
        if (!this.shouldMonitorRequest(details)) return;

        const requestData = this.requestData.get(details.requestId);
        if (requestData) {
            requestData.requestHeaders = details.requestHeaders;
            requestData.sendTime = details.timeStamp;
        }
    }

    handleHeadersReceived(details) {
        if (!this.shouldMonitorRequest(details)) return;

        const requestData = this.requestData.get(details.requestId);
        if (requestData) {
            requestData.responseHeaders = details.responseHeaders;
            requestData.statusCode = details.statusCode;
            requestData.statusLine = details.statusLine;
            requestData.receiveTime = details.timeStamp;
        }
    }

    handleCompleted(details) {
        if (!this.shouldMonitorRequest(details)) return;

        const requestData = this.requestData.get(details.requestId);
        if (requestData) {
            requestData.completed = true;
            requestData.completedTime = details.timeStamp;
            requestData.responseTime = details.timeStamp - requestData.timestamp;
            
            // 估算响应大小
            requestData.size = this.estimateResponseSize(requestData);
            
            // 发送到popup
            this.sendRequestToPopup(requestData);
        }
    }

    handleError(details) {
        if (!this.shouldMonitorRequest(details)) return;

        const requestData = this.requestData.get(details.requestId);
        if (requestData) {
            requestData.error = details.error;
            requestData.completed = false;
            requestData.statusCode = 0;
            requestData.responseTime = details.timeStamp - requestData.timestamp;
            requestData.size = 0;
            
            // 发送到popup
            this.sendRequestToPopup(requestData);
        }
    }

    shouldMonitorRequest(details) {
        // 只监控当前分析的标签页
        if (!this.isMonitoring || details.tabId !== this.currentTabId) {
            return false;
        }

        // 过滤掉扩展自身的请求
        if (details.url.startsWith('chrome-extension://')) {
            return false;
        }

        // 只监控主要的资源类型
        const allowedTypes = [
            'main_frame', 'sub_frame', 'stylesheet', 'script', 
            'image', 'font', 'object', 'xmlhttprequest', 'ping',
            'csp_report', 'media', 'websocket', 'other'
        ];

        return allowedTypes.includes(details.type);
    }

    estimateResponseSize(requestData) {
        // 尝试从响应头获取Content-Length
        if (requestData.responseHeaders) {
            const contentLength = requestData.responseHeaders.find(
                header => header.name.toLowerCase() === 'content-length'
            );
            if (contentLength) {
                return parseInt(contentLength.value) || 0;
            }
        }

        // 根据资源类型估算大小
        const sizeEstimates = {
            'main_frame': 50000,
            'sub_frame': 20000,
            'stylesheet': 15000,
            'script': 25000,
            'image': 30000,
            'font': 40000,
            'xmlhttprequest': 5000,
            'other': 10000
        };

        return sizeEstimates[requestData.type] || 10000;
    }

    async sendRequestToPopup(requestData) {
        try {
            // 简化数据以发送到popup
            const simplifiedData = {
                url: requestData.url,
                method: requestData.method,
                statusCode: requestData.statusCode || 0,
                size: requestData.size || 0,
                responseTime: requestData.responseTime || 0,
                timestamp: requestData.timestamp,
                type: requestData.type,
                error: requestData.error
            };

            // 尝试发送到popup
            await this.notifyPopup('networkRequest', simplifiedData);
            
        } catch (error) {
            console.error('发送请求数据到popup失败:', error);
        }
    }

    async notifyPopup(type, data) {
        try {
            await chrome.runtime.sendMessage({
                type: type,
                data: data
            });
        } catch (error) {
            // Popup可能已关闭，这是正常情况
            console.log('Popup未打开或已关闭');
        }
    }

    clearRequestData() {
        this.requestData.clear();
    }
}

// 初始化网络监控器
const networkMonitor = new NetworkMonitor();

// 处理扩展安装
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        console.log('网络流量分析器已安装');
    }
});
