# Chrome插件安装指南

## 快速安装步骤

### 1. 准备插件文件

确保您有以下文件：
- `manifest.json`
- `popup.html`, `popup.css`, `popup.js`
- `background.js`
- `content.js`
- `devtools.html`, `devtools.js`
- `panel.html`, `panel.css`, `panel.js`
- `icons/` 文件夹（包含图标文件）

### 2. 生成图标文件

1. 在浏览器中打开 `icons/create_icons.html`
2. 页面会自动下载4个图标文件
3. 将下载的文件重命名并放入 `icons/` 文件夹：
   - `icon16.png`
   - `icon32.png`
   - `icon48.png`
   - `icon128.png`

### 3. 安装到Chrome

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载插件**
   - 点击"加载已解压的扩展程序"
   - 选择包含所有插件文件的文件夹
   - 点击"选择文件夹"

4. **确认安装**
   - 插件应该出现在扩展列表中
   - 浏览器工具栏会显示插件图标

## 使用方法

### 基本操作

1. **点击插件图标** - 打开分析界面
2. **输入网址** - 在输入框中输入要分析的URL
3. **开始分析** - 点击"开始分析"按钮
4. **查看结果** - 实时查看网络流量分析结果

### 高级功能

- **详细面板** - 点击"打开详细面板"查看更多分析
- **导出数据** - 保存分析结果为JSON文件
- **清除数据** - 重置所有统计数据

## 故障排除

### 插件无法加载

**可能原因：**
- 文件路径错误
- manifest.json语法错误
- 缺少必要文件

**解决方法：**
1. 检查所有文件是否在正确位置
2. 验证manifest.json语法
3. 查看Chrome扩展页面的错误信息

### 网络监听不工作

**可能原因：**
- 权限不足
- 目标网站限制
- 浏览器安全策略

**解决方法：**
1. 确认插件权限已授予
2. 尝试不同的网站
3. 重新加载插件

### 数据不显示

**可能原因：**
- 存储权限问题
- 数据格式错误
- 脚本执行错误

**解决方法：**
1. 检查浏览器控制台错误
2. 重新安装插件
3. 清除浏览器缓存

## 卸载插件

1. 打开 `chrome://extensions/`
2. 找到"网络流量分析器"
3. 点击"移除"按钮
4. 确认卸载

## 更新插件

1. 修改插件文件
2. 在扩展页面点击刷新按钮
3. 或者重新加载整个插件

---

如有其他问题，请查看完整的README.md文档或联系开发者。
