#!/bin/bash

# 网络流量分析器 Chrome 扩展打包脚本 - 域名分析增强版
# 状态码中文显示 + 统一时间计算 + 域名状态统计

echo "开始打包网络流量分析器 Chrome 扩展 - 域名分析增强版..."

# 创建临时目录
TEMP_DIR="temp_package_domain_enhanced"
ZIP_NAME="网络流量分析器-Chrome插件-域名分析增强版.zip"

# 清理之前的临时目录
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

# 创建新的临时目录
mkdir "$TEMP_DIR"

# 复制所有必要文件到临时目录
echo "复制文件..."
cp chrome-extension-package/manifest.json "$TEMP_DIR/"
cp chrome-extension-package/background.js "$TEMP_DIR/"
cp chrome-extension-package/content.js "$TEMP_DIR/"
cp chrome-extension-package/popup.html "$TEMP_DIR/"
cp chrome-extension-package/popup.css "$TEMP_DIR/"
cp chrome-extension-package/popup.js "$TEMP_DIR/"
cp chrome-extension-package/panel.html "$TEMP_DIR/"
cp chrome-extension-package/panel.css "$TEMP_DIR/"
cp chrome-extension-package/panel.js "$TEMP_DIR/"
cp chrome-extension-package/devtools.html "$TEMP_DIR/"
cp chrome-extension-package/devtools.js "$TEMP_DIR/"

# 复制图标目录
cp -r chrome-extension-package/icons "$TEMP_DIR/"

# 创建更新说明文件
cat > "$TEMP_DIR/DOMAIN_ENHANCED_UPDATE_NOTES.txt" << 'EOF'
网络流量分析器 Chrome 扩展 - 域名分析增强版

🚀 本次更新重点：

1. 域名状态统计 📊
   ✅ 在域名分析中标记成功响应和未响应的次数
   ✅ 显示每个域名的成功率、无响应率、错误率
   ✅ 可视化状态统计，一目了然

2. 状态码中文显示 📋
   ✅ 所有状态码显示为中文（成功、未找到、无响应等）
   ✅ 支持常见状态码的详细中文描述
   ✅ 特殊处理无响应状态（状态码0）

3. 统一时间计算 ⏰
   ✅ 修复时间计算偏移问题
   ✅ 统一所有请求包的起始时间基准
   ✅ 限制时间显示小数位不超过两位

🎯 域名状态统计详情：

主界面域名列表：
- 域名卡片式显示，更美观
- 成功请求：✅ 绿色背景，显示成功次数和百分比
- 无响应请求：⚠️ 黄色背景，脉动动画提醒
- 错误请求：❌ 红色背景，显示错误次数

独立面板域名网格：
- 在原有统计基础上增加状态统计
- 分隔线区分基础统计和状态统计
- 相同的颜色编码和图标系统

📊 状态统计示例：

域名：api.example.com (总请求: 15)
✅ 成功: 12 (80%)
⚠️ 无响应: 2 (13%)
❌ 错误: 1 (7%)

域名：timeout.example.com (总请求: 5)
⚠️ 无响应: 5 (100%)

🔍 实用价值：

1. 快速识别问题域名：
   - 无响应率高的域名可能有网络问题
   - 错误率高的域名可能有服务问题
   - 成功率低的域名需要重点关注

2. 网络质量评估：
   - 整体成功率反映网络质量
   - 无响应率反映连接稳定性
   - 错误分布反映服务健康度

3. 问题排查指导：
   - 优先检查无响应率高的域名
   - 分析错误类型和分布
   - 对比不同域名的表现

🎨 界面改进：

域名卡片设计：
- 圆角卡片，阴影效果
- 悬停时轻微上浮
- 清晰的信息层次

状态指示器：
- ✅ 成功：绿色背景
- ⚠️ 无响应：黄色背景 + 脉动动画
- ❌ 错误：红色背景

百分比显示：
- 直观的百分比统计
- 只显示存在的状态类型
- 自动计算比例

🔧 技术实现：

数据结构扩展：
```javascript
domainData = {
    count: 总请求数,
    successCount: 成功请求数,
    noResponseCount: 无响应请求数,
    errorCount: 错误请求数,
    // ... 其他统计
}
```

状态分类逻辑：
- 成功：状态码 200-299
- 无响应：状态码 0
- 错误：其他状态码

CSS动画效果：
- 无响应状态脉动提醒
- 卡片悬停效果
- 平滑过渡动画

📋 使用场景：

1. API调试：
   - 快速识别哪些API有问题
   - 查看API成功率和错误分布
   - 发现超时和连接问题

2. 网站性能分析：
   - 评估第三方服务稳定性
   - 识别慢速或不可达的资源
   - 优化资源加载策略

3. 网络问题诊断：
   - 定位网络连接问题
   - 分析域名解析问题
   - 识别服务器响应问题

🎯 实际效果：

现在您可以：
✅ 一眼看出哪些域名有问题
✅ 快速评估网络连接质量
✅ 精确定位无响应的域名
✅ 分析不同域名的表现差异
✅ 获得详细的状态统计信息

特别适合：
- 网络问题排查
- API服务监控
- 第三方服务评估
- 网站性能优化

这个版本让域名分析功能更加强大和实用！
EOF

# 创建ZIP文件
echo "创建ZIP文件..."
cd "$TEMP_DIR"
zip -r "../$ZIP_NAME" .
cd ..

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "文件名: $ZIP_NAME"
echo ""
echo "🚀 域名分析增强版特性："
echo "✅ 域名状态统计（成功、无响应、错误次数和百分比）"
echo "✅ 状态码中文显示（成功、未找到、无响应等）"
echo "✅ 统一时间计算基准，修复时间偏移问题"
echo "✅ 可视化状态指示器（✅⚠️❌图标 + 颜色编码）"
echo ""
echo "🎯 主要改进："
echo "📊 域名分析新增状态统计，一目了然"
echo "🔍 快速识别问题域名和无响应域名"
echo "📈 百分比显示，直观评估域名质量"
echo "🎨 卡片式设计，更美观的界面"
echo ""
echo "🔍 现在可以轻松："
echo "- 识别哪些域名没有响应"
echo "- 查看每个域名的成功率"
echo "- 快速定位网络问题"
echo "- 评估服务质量"
echo ""
echo "🎉 特别适合网络问题诊断和服务监控！"
