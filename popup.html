<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络流量分析器</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🌐 网络流量分析器</h1>
            <p>实时监控网页访问的域名和流量包</p>
        </header>

        <div class="url-section">
            <div class="input-group">
                <input type="url" id="urlInput" placeholder="输入要分析的网址 (例: https://www.baidu.com)" />
                <button id="analyzeBtn" class="btn-primary">
                    <span id="btnText">开始分析</span>
                    <div id="btnLoader" class="loader hidden"></div>
                </button>
            </div>
            <div class="quick-urls">
                <span>快速选择:</span>
                <button class="quick-btn" data-url="https://www.baidu.com">百度</button>
                <button class="quick-btn" data-url="https://www.taobao.com">淘宝</button>
                <button class="quick-btn" data-url="https://www.github.com">GitHub</button>
            </div>
        </div>

        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-number" id="totalRequests">0</div>
                <div class="stat-label">总请求数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueDomains">0</div>
                <div class="stat-label">唯一域名</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSize">0KB</div>
                <div class="stat-label">传输数据</div>
            </div>
        </div>

        <div class="domains-section">
            <h3>🏷️ 访问的域名</h3>
            <div id="domainsList" class="domains-list">
                <div class="empty-state">
                    <p>开始分析后，这里将显示访问的域名</p>
                </div>
            </div>
        </div>

        <div class="packets-section">
            <h3>📦 最新数据包</h3>
            <div id="packetsList" class="packets-list">
                <div class="empty-state">
                    <p>开始分析后，这里将显示网络请求详情</p>
                </div>
            </div>
        </div>

        <div class="controls">
            <button id="clearBtn" class="btn-secondary">清除数据</button>
            <button id="exportBtn" class="btn-secondary">导出报告</button>
            <button id="devtoolsBtn" class="btn-secondary">打开详细面板</button>
        </div>

        <div class="status-bar">
            <span id="statusText">就绪</span>
            <div class="status-indicator" id="statusIndicator"></div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
