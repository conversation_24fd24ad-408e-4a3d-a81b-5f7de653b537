#!/usr/bin/env python3
"""
直接创建PNG图标文件
使用简单的像素绘制方法，不依赖复杂的图像库
"""

import struct
import zlib
import os

def create_png_data(width, height, pixels):
    """创建PNG文件数据"""
    
    def write_chunk(chunk_type, data):
        """写入PNG块"""
        chunk_head = struct.pack("!I", len(data))
        chunk_head += chunk_type
        chunk_head += data
        chunk_head += struct.pack("!I", 0xFFFFFFFF & zlib.crc32(chunk_type + data))
        return chunk_head

    # PNG文件头
    png_header = b'\x89PNG\r\n\x1a\n'
    
    # IHDR块
    ihdr_data = struct.pack("!2I5B", width, height, 8, 2, 0, 0, 0)
    ihdr_chunk = write_chunk(b'IHDR', ihdr_data)
    
    # 准备图像数据
    raw_data = b''
    for y in range(height):
        raw_data += b'\x00'  # 滤波器类型
        for x in range(width):
            pixel = pixels[y * width + x]
            raw_data += struct.pack("!3B", pixel[0], pixel[1], pixel[2])
    
    # 压缩图像数据
    compressor = zlib.compressobj()
    compressed_data = compressor.compress(raw_data)
    compressed_data += compressor.flush()
    
    # IDAT块
    idat_chunk = write_chunk(b'IDAT', compressed_data)
    
    # IEND块
    iend_chunk = write_chunk(b'IEND', b'')
    
    return png_header + ihdr_chunk + idat_chunk + iend_chunk

def create_network_icon(size):
    """创建网络图标像素数据"""
    pixels = []
    
    # 计算缩放比例
    scale = size / 128.0
    
    # 定义颜色
    bg_start = (102, 126, 234)  # #667eea
    bg_end = (118, 75, 162)     # #764ba2
    white = (255, 255, 255)
    
    # 节点位置
    nodes = [
        (int(32 * scale), int(32 * scale)),
        (int(96 * scale), int(32 * scale)),
        (int(64 * scale), int(64 * scale)),
        (int(32 * scale), int(96 * scale)),
        (int(96 * scale), int(96 * scale))
    ]
    
    # 生成每个像素
    for y in range(size):
        for x in range(size):
            # 创建渐变背景
            t = (x + y) / (2 * size)
            r = int(bg_start[0] + (bg_end[0] - bg_start[0]) * t)
            g = int(bg_start[1] + (bg_end[1] - bg_start[1]) * t)
            b = int(bg_start[2] + (bg_end[2] - bg_start[2]) * t)
            pixel = (r, g, b)
            
            # 检查是否在连接线上
            line_width = max(1, int(2 * scale))
            
            # 连接线：左上到中心
            if abs((y - nodes[0][1]) * (nodes[2][0] - nodes[0][0]) - (x - nodes[0][0]) * (nodes[2][1] - nodes[0][1])) < line_width * 1000:
                if min(nodes[0][0], nodes[2][0]) <= x <= max(nodes[0][0], nodes[2][0]) and \
                   min(nodes[0][1], nodes[2][1]) <= y <= max(nodes[0][1], nodes[2][1]):
                    pixel = white
            
            # 连接线：右上到中心
            if abs((y - nodes[1][1]) * (nodes[2][0] - nodes[1][0]) - (x - nodes[1][0]) * (nodes[2][1] - nodes[1][1])) < line_width * 1000:
                if min(nodes[1][0], nodes[2][0]) <= x <= max(nodes[1][0], nodes[2][0]) and \
                   min(nodes[1][1], nodes[2][1]) <= y <= max(nodes[1][1], nodes[2][1]):
                    pixel = white
            
            # 连接线：中心到左下
            if abs((y - nodes[2][1]) * (nodes[3][0] - nodes[2][0]) - (x - nodes[2][0]) * (nodes[3][1] - nodes[2][1])) < line_width * 1000:
                if min(nodes[2][0], nodes[3][0]) <= x <= max(nodes[2][0], nodes[3][0]) and \
                   min(nodes[2][1], nodes[3][1]) <= y <= max(nodes[2][1], nodes[3][1]):
                    pixel = white
            
            # 连接线：中心到右下
            if abs((y - nodes[2][1]) * (nodes[4][0] - nodes[2][0]) - (x - nodes[2][0]) * (nodes[4][1] - nodes[2][1])) < line_width * 1000:
                if min(nodes[2][0], nodes[4][0]) <= x <= max(nodes[2][0], nodes[4][0]) and \
                   min(nodes[2][1], nodes[4][1]) <= y <= max(nodes[2][1], nodes[4][1]):
                    pixel = white
            
            # 检查是否在节点上
            node_radius = max(2, int(4 * scale))
            for node in nodes:
                distance = ((x - node[0]) ** 2 + (y - node[1]) ** 2) ** 0.5
                if distance <= node_radius:
                    pixel = white
                    break
            
            pixels.append(pixel)
    
    return pixels

def main():
    """主函数"""
    print("🎨 开始生成PNG图标文件...")
    
    # 确保目录存在
    for directory in ['icons', 'chrome-extension-package/icons']:
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    # 生成不同尺寸的图标
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        print(f"📝 生成 {size}x{size} 图标...")
        
        # 创建像素数据
        pixels = create_network_icon(size)
        
        # 创建PNG数据
        png_data = create_png_data(size, size, pixels)
        
        # 保存到两个位置
        for directory in ['icons', 'chrome-extension-package/icons']:
            filename = f"{directory}/icon{size}.png"
            with open(filename, 'wb') as f:
                f.write(png_data)
            print(f"✅ 已保存: {filename}")
    
    print("🎉 所有PNG图标文件生成完成！")
    print("\n现在可以安装Chrome插件了：")
    print("1. 打开 chrome://extensions/")
    print("2. 开启'开发者模式'")
    print("3. 点击'加载已解压的扩展程序'")
    print("4. 选择 chrome-extension-package 文件夹")

if __name__ == "__main__":
    main()
