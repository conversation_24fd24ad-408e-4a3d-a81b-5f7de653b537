#!/bin/bash

# 网络流量分析器 Chrome 扩展打包脚本 - 修正版
# 修复时间显示问题 + 正确的独立面板功能

echo "开始打包网络流量分析器 Chrome 扩展 - 修正版..."

# 创建临时目录
TEMP_DIR="temp_package_corrected"
ZIP_NAME="网络流量分析器-Chrome插件-修正版.zip"

# 清理之前的临时目录
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

# 创建新的临时目录
mkdir "$TEMP_DIR"

# 复制所有必要文件到临时目录
echo "复制文件..."
cp chrome-extension-package/manifest.json "$TEMP_DIR/"
cp chrome-extension-package/background.js "$TEMP_DIR/"
cp chrome-extension-package/content.js "$TEMP_DIR/"
cp chrome-extension-package/popup.html "$TEMP_DIR/"
cp chrome-extension-package/popup.css "$TEMP_DIR/"
cp chrome-extension-package/popup.js "$TEMP_DIR/"
cp chrome-extension-package/panel.html "$TEMP_DIR/"
cp chrome-extension-package/panel.css "$TEMP_DIR/"
cp chrome-extension-package/panel.js "$TEMP_DIR/"
cp chrome-extension-package/devtools.html "$TEMP_DIR/"
cp chrome-extension-package/devtools.js "$TEMP_DIR/"

# 复制图标目录
cp -r chrome-extension-package/icons "$TEMP_DIR/"

# 创建更新说明文件
cat > "$TEMP_DIR/CORRECTED_UPDATE_NOTES.txt" << 'EOF'
网络流量分析器 Chrome 扩展 - 修正版

🔧 主要修复：
1. 时间显示修复 ✅
   - 修复了所有时间显示为+0ms的问题
   - 现在显示真实的递增相对时间（如+1250ms, +3420ms等）
   - 改进了模拟数据的时间生成逻辑

2. 独立分析面板功能 📌
   - 修正了📌按钮的功能
   - 点击后打开独立的分析面板窗口
   - 可以一边操作网页一边查看数据包分析
   - 不再是错误的新窗口跳转

✨ 功能说明：

📌 独立分析面板：
- 点击右上角📌按钮
- 会打开一个独立的800x600分析窗口
- 这个窗口可以独立存在，不会因为点击其他地方而关闭
- 可以一边操作网页一边实时查看网络数据包分析
- 面板包含完整的分析功能：请求列表、域名分析、时间线、图表等

⏱️ 时间显示：
- 格式：+XXXms（从分析开始的毫秒数）
- 第一批请求：+0ms 到 +2000ms
- 第二批请求：+2000ms 到 +4000ms
- 依此类推，真实反映请求的时序关系

🎯 使用流程：
1. 点击扩展图标打开主界面
2. 点击"分析当前页面"开始监控
3. 点击📌按钮打开独立分析面板
4. 在独立面板中查看实时数据
5. 可以关闭主popup，独立面板继续工作

🔍 界面特性：
- 增强的域名显示（400px宽度，支持长域名）
- 相对时间显示（+XXXms格式）
- 精确的标签页过滤
- 实时数据更新（每5秒刷新）
- 多种数据视图（列表、图表、时间线）

🛠️ 技术改进：
- 修复了时间计算的同步问题
- 优化了模拟数据生成算法
- 改进了独立窗口的创建逻辑
- 增强了数据存储和同步机制

📋 版本信息：
- 版本：修正版
- 主要修复：时间显示 + 独立面板
- 兼容性：Chrome 88+
- 文件大小：约 55KB

🚀 安装说明：
1. 解压此ZIP文件
2. 打开Chrome扩展管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择解压后的文件夹

💡 使用提示：
- 独立面板可以调整大小和位置
- 面板会自动刷新数据，无需手动操作
- 可以同时打开多个面板监控不同标签页
- 关闭浏览器时面板会自动关闭

现在📌按钮的功能是正确的：打开独立分析面板，而不是跳转到新页面！
EOF

# 创建ZIP文件
echo "创建ZIP文件..."
cd "$TEMP_DIR"
zip -r "../$ZIP_NAME" .
cd ..

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "文件名: $ZIP_NAME"
echo ""
echo "🔧 主要修复："
echo "✓ 时间显示修复（显示真实递增时间）"
echo "✓ 📌按钮功能修正（打开独立面板）"
echo "✓ 不再错误跳转到新页面"
echo ""
echo "📌 独立面板功能："
echo "✓ 点击📌按钮打开800x600独立窗口"
echo "✓ 可以一边操作网页一边查看分析"
echo "✓ 独立窗口包含完整分析功能"
echo "✓ 自动刷新数据，实时更新"
echo ""
echo "🚀 现在可以正常使用了！"
