#!/bin/bash

# 网络流量分析器 Chrome 扩展打包脚本 - 更新版
# 包含时间显示优化、域名显示增强和标签页过滤改进

echo "开始打包网络流量分析器 Chrome 扩展 - 更新版..."

# 创建临时目录
TEMP_DIR="temp_package_updated"
ZIP_NAME="网络流量分析器-Chrome插件-优化时间显示版.zip"

# 清理之前的临时目录
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

# 创建新的临时目录
mkdir "$TEMP_DIR"

# 复制所有必要文件到临时目录
echo "复制文件..."
cp chrome-extension-package/manifest.json "$TEMP_DIR/"
cp chrome-extension-package/background.js "$TEMP_DIR/"
cp chrome-extension-package/content.js "$TEMP_DIR/"
cp chrome-extension-package/popup.html "$TEMP_DIR/"
cp chrome-extension-package/popup.css "$TEMP_DIR/"
cp chrome-extension-package/popup.js "$TEMP_DIR/"
cp chrome-extension-package/panel.html "$TEMP_DIR/"
cp chrome-extension-package/panel.css "$TEMP_DIR/"
cp chrome-extension-package/panel.js "$TEMP_DIR/"
cp chrome-extension-package/devtools.html "$TEMP_DIR/"
cp chrome-extension-package/devtools.js "$TEMP_DIR/"

# 复制图标目录
cp -r chrome-extension-package/icons "$TEMP_DIR/"

# 创建更新说明文件
cat > "$TEMP_DIR/UPDATE_NOTES.txt" << 'EOF'
网络流量分析器 Chrome 扩展 - 优化时间显示版

更新内容：
1. 时间显示优化
   - 将标准时间格式改为相对时间显示
   - 显示从网页开始分析到数据包产生的时间差（毫秒）
   - 格式：+XXXms（表示分析开始后XXX毫秒产生的数据包）

2. 域名显示增强
   - 增大域名显示框的尺寸（从300px增加到400px）
   - 增加域名卡片的高度和内边距
   - 改善域名文本的显示，支持多行显示
   - 增加最小高度确保更好的可读性

3. 标签页过滤改进
   - 强化了只捕获当前标签页数据包的逻辑
   - 添加了更详细的过滤日志
   - 过滤掉Chrome内部请求和扩展自身请求
   - 确保数据包来源的准确性

安装说明：
1. 打开 Chrome 浏览器
2. 进入扩展管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择解压后的文件夹

使用说明：
1. 点击扩展图标打开分析界面
2. 点击"分析当前页面"开始监控
3. 在"请求列表"标签页查看相对时间显示
4. 在"域名分析"标签页查看增强的域名显示
5. 所有时间显示为"+XXXms"格式，表示相对于分析开始的时间

技术改进：
- 在background.js中添加了监控开始时间记录
- 在panel.js和popup.js中实现了相对时间计算
- 优化了CSS样式以改善域名显示
- 增强了标签页过滤逻辑的准确性
EOF

# 创建ZIP文件
echo "创建ZIP文件..."
cd "$TEMP_DIR"
zip -r "../$ZIP_NAME" .
cd ..

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "文件名: $ZIP_NAME"
echo ""
echo "主要更新："
echo "✓ 时间显示改为相对时间格式 (+XXXms)"
echo "✓ 域名显示框增大，支持更多行显示"
echo "✓ 强化标签页过滤，确保只捕获当前标签页数据"
echo ""
echo "安装方法："
echo "1. 解压 $ZIP_NAME"
echo "2. 在Chrome扩展管理页面加载解压后的文件夹"
echo "3. 开始使用优化后的网络分析功能"
