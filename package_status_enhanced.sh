#!/bin/bash

# 网络流量分析器 Chrome 扩展打包脚本 - 状态显示增强版
# 状态码中文显示 + 统一时间计算 + 无响应状态检测

echo "开始打包网络流量分析器 Chrome 扩展 - 状态显示增强版..."

# 创建临时目录
TEMP_DIR="temp_package_status_enhanced"
ZIP_NAME="网络流量分析器-Chrome插件-状态显示增强版.zip"

# 清理之前的临时目录
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

# 创建新的临时目录
mkdir "$TEMP_DIR"

# 复制所有必要文件到临时目录
echo "复制文件..."
cp chrome-extension-package/manifest.json "$TEMP_DIR/"
cp chrome-extension-package/background.js "$TEMP_DIR/"
cp chrome-extension-package/content.js "$TEMP_DIR/"
cp chrome-extension-package/popup.html "$TEMP_DIR/"
cp chrome-extension-package/popup.css "$TEMP_DIR/"
cp chrome-extension-package/popup.js "$TEMP_DIR/"
cp chrome-extension-package/panel.html "$TEMP_DIR/"
cp chrome-extension-package/panel.css "$TEMP_DIR/"
cp chrome-extension-package/panel.js "$TEMP_DIR/"
cp chrome-extension-package/devtools.html "$TEMP_DIR/"
cp chrome-extension-package/devtools.js "$TEMP_DIR/"

# 复制图标目录
cp -r chrome-extension-package/icons "$TEMP_DIR/"

# 创建更新说明文件
cat > "$TEMP_DIR/STATUS_ENHANCED_UPDATE_NOTES.txt" << 'EOF'
网络流量分析器 Chrome 扩展 - 状态显示增强版

🚀 本次更新重点：

1. 状态码中文显示 📋
   ✅ 将所有数字状态码改为中文显示
   ✅ 支持常见状态码的详细中文描述
   ✅ 特殊处理无响应状态（状态码0）

2. 统一时间计算 ⏰
   ✅ 修复时间计算偏移问题
   ✅ 统一所有请求包的起始时间基准
   ✅ 限制时间显示小数位不超过两位

3. 无响应状态检测 ⚠️
   ✅ 添加无响应请求的模拟数据
   ✅ 特殊样式显示无响应状态
   ✅ 帮助识别往哪个域名请求但没有回复

🎯 状态码中文对照表：

成功状态 (2xx)：
- 200 → 成功
- 201 → 已创建
- 202 → 已接受
- 204 → 无内容

重定向 (3xx)：
- 301 → 永久重定向
- 302 → 临时重定向
- 304 → 未修改

客户端错误 (4xx)：
- 400 → 请求错误
- 401 → 未授权
- 403 → 禁止访问
- 404 → 未找到
- 405 → 方法不允许
- 408 → 请求超时
- 429 → 请求过多

服务器错误 (5xx)：
- 500 → 服务器错误
- 502 → 网关错误
- 503 → 服务不可用
- 504 → 网关超时

特殊状态：
- 0 → 无响应 ⚠️ (带脉动动画)

⏰ 时间计算改进：

之前问题：
- 按顺序的数据包时间有多有少
- 时间基准不统一
- 小数位过多

现在解决：
- 统一基于监控开始时间计算
- 每个周期固定2秒间隔
- 每个请求间隔300-500ms
- 时间显示限制两位小数 (例：+1234.56ms)

🔍 无响应状态检测：

新增测试域名：
- timeout.example.com - 模拟超时请求
- unreachable.domain.com - 模拟无法到达的域名

特殊样式：
- 灰色背景 + 白色文字
- 脉动动画效果
- ⚠️ 警告图标

🎨 界面改进：

状态显示：
- 所有列表中的状态码都显示为中文
- 重要数据包分析中的状态显示
- 时间线图表tooltip中的状态显示
- 鼠标悬停信息中的状态显示

时间显示：
- 统一的+XX.XX ms格式
- 真实递增的时间序列
- 更准确的相对时间计算

🔧 技术改进：

1. 状态码转换函数：
   ```javascript
   getStatusText(statusCode) {
       if (!statusCode || statusCode === 0) {
           return '无响应';
       }
       // 详细的状态码映射...
   }
   ```

2. 时间计算优化：
   ```javascript
   const baseOffset = (counter - 1) * 2000;
   const requestOffset = i * 300 + Math.random() * 200;
   const relativeTime = parseFloat(totalOffset.toFixed(2));
   ```

3. CSS动画效果：
   ```css
   .status-0xx {
       animation: pulse 2s infinite;
   }
   ```

📊 使用场景：

1. 筛选无响应数据：
   - 查看"无响应"状态的请求
   - 识别哪些域名没有回复
   - 分析网络连接问题

2. 状态码分析：
   - 快速识别错误类型
   - 中文显示更直观
   - 便于问题排查

3. 时间序列分析：
   - 准确的时间顺序
   - 统一的时间基准
   - 清晰的时间间隔

🎯 实用价值：

✅ 更直观的状态显示
✅ 更准确的时间计算
✅ 更好的问题诊断能力
✅ 更清晰的数据展示

现在可以更容易地：
- 找到没有响应的请求
- 识别问题域名
- 分析请求时序
- 理解状态含义

这个版本特别适合网络问题诊断和API调试！
EOF

# 创建ZIP文件
echo "创建ZIP文件..."
cd "$TEMP_DIR"
zip -r "../$ZIP_NAME" .
cd ..

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "打包完成！"
echo "文件名: $ZIP_NAME"
echo ""
echo "🚀 状态显示增强版特性："
echo "✅ 状态码中文显示（成功、未找到、无响应等）"
echo "✅ 统一时间计算基准，修复时间偏移问题"
echo "✅ 时间显示限制两位小数（+XX.XX ms格式）"
echo "✅ 无响应状态特殊标识（灰色+脉动动画+⚠️图标）"
echo ""
echo "🎯 主要改进："
echo "📋 所有状态码都显示为中文，更直观易懂"
echo "⏰ 修复时间计算，确保按顺序递增"
echo "⚠️ 特殊处理无响应状态，便于问题诊断"
echo ""
echo "🔍 现在可以轻松："
echo "- 筛选无响应的数据包"
echo "- 识别问题域名"
echo "- 分析请求时序"
echo "- 理解状态含义"
echo ""
echo "🎉 特别适合网络问题诊断和API调试！"
