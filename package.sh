#!/bin/bash

# Chrome插件打包脚本

echo "🚀 开始打包Chrome插件..."

# 创建打包目录
PACKAGE_DIR="chrome-extension-package"
rm -rf $PACKAGE_DIR
mkdir $PACKAGE_DIR

echo "📁 复制插件文件..."

# 复制必要文件
cp manifest.json $PACKAGE_DIR/
cp popup.html $PACKAGE_DIR/
cp popup.css $PACKAGE_DIR/
cp popup.js $PACKAGE_DIR/
cp background.js $PACKAGE_DIR/
cp content.js $PACKAGE_DIR/
cp devtools.html $PACKAGE_DIR/
cp devtools.js $PACKAGE_DIR/
cp panel.html $PACKAGE_DIR/
cp panel.css $PACKAGE_DIR/
cp panel.js $PACKAGE_DIR/

# 复制图标文件夹
cp -r icons $PACKAGE_DIR/

echo "📋 创建安装说明..."

# 创建简化的安装说明
cat > $PACKAGE_DIR/INSTALL.txt << 'EOF'
Chrome插件安装说明
==================

1. 打开Chrome浏览器
2. 访问 chrome://extensions/
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择此文件夹
6. 确认安装

使用方法：
- 点击浏览器工具栏中的插件图标
- 输入要分析的网址
- 点击"开始分析"

注意：如果icons文件夹中没有图标文件，请：
1. 在浏览器中打开 icons/create_icons.html
2. 下载生成的图标文件
3. 重命名为 icon16.png, icon32.png, icon48.png, icon128.png
4. 放入icons文件夹中
EOF

echo "🎯 检查图标文件..."

# 检查图标文件是否存在
if [ ! -f "icons/icon16.png" ]; then
    echo "⚠️  警告: 缺少图标文件，请运行 icons/create_icons.html 生成图标"
    echo "📝 创建图标生成提示..."
    
    cat > $PACKAGE_DIR/GENERATE_ICONS.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>生成插件图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        canvas { border: 1px solid #ccc; margin: 20px; }
        button { padding: 10px 20px; margin: 5px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>🎨 生成插件图标</h1>
    <p>点击下面的按钮生成并下载图标文件</p>
    
    <canvas id="canvas" width="128" height="128"></canvas>
    
    <div>
        <button onclick="downloadIcon(16)">下载 16x16</button>
        <button onclick="downloadIcon(32)">下载 32x32</button>
        <button onclick="downloadIcon(48)">下载 48x48</button>
        <button onclick="downloadIcon(128)">下载 128x128</button>
        <button onclick="downloadAll()">下载全部</button>
    </div>
    
    <p><strong>下载后请将文件重命名为：</strong></p>
    <ul style="text-align: left; display: inline-block;">
        <li>icon16.png</li>
        <li>icon32.png</li>
        <li>icon48.png</li>
        <li>icon128.png</li>
    </ul>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制图标
        function drawIcon() {
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 128, 128);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 128, 128);
            
            // 绘制网络图标
            ctx.strokeStyle = 'white';
            ctx.fillStyle = 'white';
            ctx.lineWidth = 4;
            
            // 绘制节点
            const nodes = [
                {x: 32, y: 32}, {x: 96, y: 32},
                {x: 64, y: 64}, {x: 32, y: 96}, {x: 96, y: 96}
            ];
            
            // 绘制连接线
            ctx.beginPath();
            ctx.moveTo(32, 32);
            ctx.lineTo(64, 64);
            ctx.lineTo(96, 32);
            ctx.moveTo(64, 64);
            ctx.lineTo(32, 96);
            ctx.moveTo(64, 64);
            ctx.lineTo(96, 96);
            ctx.stroke();
            
            // 绘制节点圆圈
            nodes.forEach(node => {
                ctx.beginPath();
                ctx.arc(node.x, node.y, 8, 0, 2 * Math.PI);
                ctx.fill();
            });
        }
        
        function downloadIcon(size) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = size;
            tempCanvas.height = size;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(canvas, 0, 0, size, size);
            
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = tempCanvas.toDataURL();
            link.click();
        }
        
        function downloadAll() {
            [16, 32, 48, 128].forEach((size, index) => {
                setTimeout(() => downloadIcon(size), index * 500);
            });
        }
        
        // 初始化绘制
        drawIcon();
    </script>
</body>
</html>
EOF
else
    echo "✅ 图标文件已存在"
fi

echo "📦 创建压缩包..."

# 创建zip文件
cd $PACKAGE_DIR
zip -r "../网络流量分析器-Chrome插件.zip" .
cd ..

echo "🎉 打包完成！"
echo ""
echo "📁 打包文件位置："
echo "   - 文件夹: $PACKAGE_DIR/"
echo "   - 压缩包: 网络流量分析器-Chrome插件.zip"
echo ""
echo "📋 安装步骤："
echo "   1. 解压zip文件（如果使用压缩包）"
echo "   2. 打开Chrome浏览器"
echo "   3. 访问 chrome://extensions/"
echo "   4. 开启'开发者模式'"
echo "   5. 点击'加载已解压的扩展程序'"
echo "   6. 选择解压后的文件夹"
echo ""
echo "⚠️  注意：如果缺少图标文件，请先运行 GENERATE_ICONS.html 生成图标"
